import { route } from '../utils/index.ts';
import pingRoutes from './pingRoutes.ts';
import testApiRoutes from './testRoutes.ts';
import { securityRoutes } from '../modules/security/index.ts';
import { userRoutes } from '../modules/userManagement/index.ts';
import { twoFaRoutes } from '../modules/twoFa/index.ts';
import { staffRoutes } from '../modules/staffManagement/index.ts';
import { schoolRoutes } from '../modules/schoolManagement/index.ts';
import { rolesRoutes } from '../modules/roles/index.ts';
import { classRoutes } from '../modules/classManagement/index.ts';
import { houseRoutes } from '../modules/houseManagement/index.ts';
import { mentoringGroupRoutes } from '../modules/mentoringGroupManagement/index.ts';

// guaranteed to get dependencies
export default () => {
  pingRoutes();
  testApiRoutes();
  securityRoutes();
  rolesRoutes();
  userRoutes();
  schoolRoutes();
  twoFaRoutes();
  staffRoutes();
  classRoutes();
  houseRoutes();
  mentoringGroupRoutes();
  return route;
};
