import type { Request, Response, NextFunction } from 'express';
// import { Container } from 'typedi';
// import SchoolService from '../../modules/schoolManagement/schoolService.ts';
import { formatErrorResponse, HttpException } from '../../utils/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export async function verifySchool(
  req: Request,
  res: Response,
  next: NextFunction
) {
  const messageKey = 'verifySchool';
  try {
    const { currentUser } = req;
    if (!currentUser) {
      throw new HttpException.Unauthorized(
        formatErrorResponse(messageKey, 'invalidUser')
      );
    }

    const schoolIdHeader = req.headers['x-school-id'];
    if (!schoolIdHeader || typeof schoolIdHeader !== 'string') {
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'requiredSchoolId')
      );
    }

    const schoolId = parserId(schoolIdHeader, 'schoolId');
    // const service = Container.get(SchoolService);
    // const updatedRights = await service.validateSchool(schoolId, currentUser);

    req.currentUser = {
      ...currentUser,
      // rights: updatedRights,
      schoolId,
    };

    next();
  } catch (error) {
    next(error);
  }
}
