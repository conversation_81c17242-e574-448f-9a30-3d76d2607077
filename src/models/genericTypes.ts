import type { RoleName } from '../auth/role.ts';

export type Id = number;

export type Row = Record<string, any>;
export type ResultSet = { rows: Row[] };

export type RowMapper<T> = (_row: Row) => T;

export type Filter = {
  page: number;
  limit: number;
  allResults?: boolean;
  total?: number;
  order?: string | null;
  direction?: 'asc' | 'desc';
  filters: Record<string, any>;
  allowedFilters: string[];
};

export type MetaData = {
  order: string | null;
  direction: string;
  page: number;
  limit: number;
  total: number;
  filters: Record<string, any>;
  allowedFilters: string[];
  [key: string]: any;
};

export type PaginatedResponse<T> = {
  metadata: MetaData;
  records: T[];
};

export interface CustomError extends Error {
  status?: number;
  metaData?: any;
}

export interface CurrentUser {
  id: Id;
  tokenAud: string;
  rights: string[];
  role: {
    getId(): number;
    getRoleName(): RoleName;
  };
  roleIds: number[];
  [key: string]: any;
  lastLogin: string;
  schoolId?: number | null;
}

export type RequestMetadata = {
  ip: string;
  userAgent: string;
};
