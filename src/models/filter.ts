import express from 'express';
type Request = express.Request;

type AllFilters = typeof Filter.types;
type FilterType = AllFilters[keyof AllFilters];

interface QueryParams {
  page?: string;
  limit?: string;
  order?: string;
  direction?: string;
  allResults?: string;
  filter?: Record<string, string>;
}

export interface ParsedFilter {
  allowedFilters: string[];
  page: number;
  limit: number;
  order?: string;
  direction?: string;
  allResults?: boolean;
  filters: Record<string, string>;
}

class Filter {
  static types = Object.freeze({
    GENERAL: 'GENERAL',
    USER: 'USER',
    ROLES: 'ROLES',
    STAFFS_LIST: 'STAFFS_LIST',
    CLASSES_LIST: 'CLASSES_LIST',
  });

  static getAllowedFilter(filter: FilterType): string[] {
    const filterMap: Record<FilterType, readonly string[]> = {
      [Filter.types.GENERAL]: Object.freeze(['search']),
      [Filter.types.USER]: Object.freeze(['search']),
      [Filter.types.ROLES]: Object.freeze(['type', 'status']),
      [Filter.types.STAFFS_LIST]: Object.freeze([
        'search',
        'status',
        'order',
        'roleId',
        'classId',
        'mentorGroupId',
      ]),
      [Filter.types.CLASSES_LIST]: Object.freeze(['status', 'name']),
    };
    return Array.from(filterMap[filter] || []);
  }

  static fromRequest(
    req: Request,
    type: FilterType,
    isExport: boolean = false
  ): ParsedFilter {
    const filter: ParsedFilter = {
      allowedFilters: [...Filter.getAllowedFilter(type)],
      page: 0,
      limit: 50,
      filters: {},
    };

    const params = req.query as QueryParams;

    const page = params.page ? Math.max(0, parseInt(params.page, 10) - 1) : 0;
    const limit = params.limit ? parseInt(params.limit, 10) : 50;

    filter.page = page;
    filter.limit = limit;

    if (params.order) filter.order = params.order;
    if (params.direction) filter.direction = params.direction;
    if (params.allResults) {
      filter.allResults = params.allResults.toLowerCase() === 'true';
    }

    if (isExport) {
      filter.allResults = true;
    }

    if (!params.filter) {
      params.filter = {};
      for (const key in req.query) {
        const match = key.match(/^filter\[(.+)\]$/);
        if (match) {
          const filterKey = match[1];
          const value = req.query[key];
          if (typeof value === 'string') {
            params.filter[filterKey] = value;
          }
        }
      }
    }

    if (params.filter) {
      for (const key of filter.allowedFilters) {
        if (params.filter[key]) {
          filter.filters[key] = params.filter[key];
        }
      }
    }

    return {
      ...filter,
      allowedFilters: [...filter.allowedFilters],
      filters: { ...filter.filters },
    };
  }
}

export default Filter;
