import moment from 'moment';
import {
  Query<PERSON><PERSON>er,
  Mapper,
  Queries,
  parserId,
  parserInteger,
  parserDate,
} from '../../utils/daoHelper/index.ts';
import { STATUS } from '../../utils/index.ts';
import { userUpdateMap, userDetailsUpdateMap } from './updateMaps/index.ts';
import type { PoolClient } from 'pg';
import PasswordHash from '../../models/passwordHash.ts';
import type {
  LogPasswordResetDto,
  MappedPasswordResetToken,
  MappedUser,
  UpdatePasswordDto,
} from './types.ts';
import type { RequestMetadata, UserDto } from '../security/types.ts';
import type { CurrentUser, Id } from '../../models/genericTypes.ts';
import { RoleName } from '../../auth/role.ts';

class UserDao {
  userJoins = `LEFT JOIN user_roles ur ON ur.user_id = u.id
              LEFT JOIN roles r ON r.id = ur.role_id
              LEFT JOIN user_details ud ON ud.user_id = u.id
              LEFT JOIN users uld ON uld.id = u.id\n
              LEFT JOIN LATERAL (
                SELECT login_at
                FROM user_login_history ulh
                WHERE ulh.user_id = u.id
                ORDER BY login_at DESC
                LIMIT 1
              ) AS last_login ON true
              LEFT JOIN school_users su ON su.user_id = u.id
              LEFT JOIN schools s ON s.id = su.school_id\n`;

  userQuery = `SELECT u.id,u.email,u.password,
              CASE 
                WHEN s.status = 'INACTIVE' THEN 'INACTIVE'
                ELSE u.status
              END AS status, u.created_on, json_agg(DISTINCT jsonb_build_object('id', r.id, 'name', r.name)) AS role, array_agg(DISTINCT r.id) AS role_ids,
              CASE 
                WHEN s.id IS NOT NULL 
                THEN jsonb_build_object(
                  'id', s.id,
                  'name', s.name,
                  'status', s.status,
                  'is_owner', su.is_owner
                )
                ELSE NULL
              END AS school, ud.first_name,
              ud.last_name, ud.profile_picture_path, uld.wrong_login_count, uld.last_wrong_login_attempt,last_login.login_at AS last_login_at
              FROM users u \n${this.userJoins} `;

  groupByQuery = ` GROUP BY 
                    u.id, u.email, u.password, u.status, u.created_on,
                    ud.first_name, ud.last_name, ud.profile_picture_path,
                    uld.wrong_login_count, uld.last_wrong_login_attempt,
                    last_login.login_at, s.id, s.name, s.status, su.is_owner;`;

  static MAX_TOKEN_VALIDITY = 3600;

  async createUser(
    client: PoolClient,
    createUserDto: UserDto,
    createdBy?: number | null | undefined
  ) {
    const res = await client.query(
      `INSERT INTO users 
      (email, password, salt, status, created_by, updated_by) 
      VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [
        createUserDto.email,
        createUserDto.password,
        createUserDto.salt,
        createUserDto.status,
        createdBy,
        createdBy,
      ]
    );
    const userId = Mapper.getId(res);

    const detailsCreatedBy = createdBy || userId;

    await client.query(
      `INSERT INTO user_details
      (user_id, first_name, last_name, created_by, updated_by)
      VALUES ($1, $2, $3, $4, $5)`,
      [
        userId,
        createUserDto.firstName,
        createUserDto.lastName,
        detailsCreatedBy,
        detailsCreatedBy,
      ]
    );

    return userId;
  }

  async updateRoleByUserId(
    client: PoolClient,
    roleNames: RoleName[],
    userId: number
  ): Promise<boolean> {
    const roleIdsRes = await client.query(
      `SELECT id FROM roles WHERE name = ANY($1)`,
      [roleNames]
    );
    const incomingRoleIds = roleIdsRes.rows.map((r) => r.id);

    const currentRolesRes = await client.query(
      `SELECT role_id FROM user_roles WHERE user_id = $1`,
      [userId]
    );
    const currentRoleIds = currentRolesRes.rows.map((r) => r.role_id);

    const incomingSet = new Set(incomingRoleIds);
    const currentSet = new Set(currentRoleIds);

    const rolesToDelete = currentRoleIds.filter((id) => !incomingSet.has(id));
    const rolesToInsert = incomingRoleIds.filter((id) => !currentSet.has(id));

    if (rolesToDelete.length > 0) {
      await client.query(
        `DELETE FROM user_roles WHERE user_id = $1 AND role_id = ANY($2)`,
        [userId, rolesToDelete]
      );
    }

    if (rolesToInsert.length > 0) {
      const insertValues = rolesToInsert
        .map((id) => `(${userId}, ${id})`)
        .join(',');
      await client.query(
        `INSERT INTO user_roles (user_id, role_id)
         VALUES ${insertValues}
         ON CONFLICT DO NOTHING`
      );
    }
    return true;
  }

  async updateUser(
    client: PoolClient,
    updateUserDto: {
      id: Id;
      firstName: string | null;
      lastName: string | null;
      email: string | null;
      status: string | null;
      updatedBy: Id;
    }
  ) {
    const cleanedDto = {
      ...updateUserDto,
      email: updateUserDto.email ?? undefined,
      firstName: updateUserDto.firstName ?? undefined,
      lastName: updateUserDto.lastName ?? undefined,
      status: updateUserDto.status ?? undefined,
      updatedBy: updateUserDto.updatedBy,
      user_id: updateUserDto.id,
    };

    const { sql: sql1, args: args1 } = Queries.updaterFor(
      'users',
      userUpdateMap,
      cleanedDto
    );
    const res1 = await client.query(sql1, args1);

    const { sql: sql2, args: args2 } = Queries.updaterFor(
      'user_details',
      userDetailsUpdateMap,
      cleanedDto,
      'user_id'
    );
    const res2 = await client.query(sql2, args2);

    return res1.rowCount === 1 && res2.rowCount === 1;
  }

  async makeSchoolUser(
    client: PoolClient,
    dto: {
      userId: Id;
      schoolId: Id;
      isOwnwer: boolean;
    }
  ) {
    const query = `
      INSERT INTO school_users (user_id, school_id, is_owner)
      VALUES ($1, $2, $3)
      RETURNING *;
      `;
    const values = [dto.userId, dto.schoolId, dto.isOwnwer];
    const result = await client.query(query, values);
    return result.rows[0];
  }

  async updatePassword(client: PoolClient, dto: UpdatePasswordDto) {
    const qb = new QueryBuilder(
      `UPDATE users
       SET 
        password = ?,
        salt = ?,
        updated_by = ?
       WHERE id = ?;`,
      [dto.newPassword, dto.salt, dto.updatedBy, dto.id]
    );
    const { sql, args } = qb.build();
    const result = await client.query(sql, args);
    return result.rowCount === 1;
  }

  async findUserByEmail(
    client: PoolClient,
    email: string,
    onlyActive: boolean = false
  ) {
    const preArgs = [email];
    let query = `${this.userQuery} WHERE u.email = ?`;
    if (onlyActive) {
      query += ' AND u.status = ?';
      preArgs.push(STATUS.ACTIVE);
    }
    query += ` ${this.groupByQuery}`;
    const qb = new QueryBuilder(query, preArgs);
    const { sql, args } = qb.build();
    const res = await client.query(sql, args);
    if (!res.rows[0]) return null;

    const rolePermissions = await this.getModulesForUserOrRole(
      client,
      res.rows[0].role_ids,
      'role'
    );
    const userPermissions = await this.getModulesForUserOrRole(
      client,
      res.rows[0].id,
      'user'
    );
    const user = Mapper.getUnique(res, UserDao.mapUserWithRoles);
    return { rolePermissions, userPermissions, ...user } as MappedUser;
  }

  async findUserById(
    client: PoolClient,
    id: Id,
    onlyActive = false
  ): Promise<MappedUser | null> {
    const preArgs: any = [id];
    let query = `${this.userQuery} WHERE u.id = ?`;
    if (onlyActive) {
      query += ' AND u.status = ?';
      preArgs.push(STATUS.ACTIVE);
    }
    query += ` ${this.groupByQuery}`;
    const qb = new QueryBuilder(query, preArgs);
    const { sql, args } = qb.build();
    const res = await client.query(sql, args);
    if (!res.rows[0]) return null;

    const rolePermissions = await this.getModulesForUserOrRole(
      client,
      res.rows[0].role_ids,
      'role'
    );
    const userPermissions = await this.getModulesForUserOrRole(
      client,
      res.rows[0].id,
      'user'
    );
    const user = Mapper.getUnique(res, UserDao.mapUserWithRoles);
    return { rolePermissions, userPermissions, ...user } as MappedUser;
  }

  async getModulesForUserOrRole(
    client: PoolClient,
    ids: number[] | string[],
    type = 'role'
  ) {
    const isArray = Array.isArray(ids);

    let getQuery = '';
    let args = [];

    if (type === 'role') {
      getQuery = `
      SELECT DISTINCT p.name
      FROM permissions p
      JOIN role_permissions rp ON rp.permission_id = p.id
      WHERE ${isArray ? 'rp.role_id = ANY($1)' : 'rp.role_id = $1'}
    `;
      args = [ids];
    } else {
      getQuery = `
      SELECT DISTINCT p.name
      FROM permissions p
      JOIN user_permissions up ON up.permission_id = p.id
      WHERE up.user_id = $1
    `;
      args = [ids];
    }
    const result = await client.query(getQuery, args);
    return result.rows.map((row) => row.name);
  }

  async markUserLogin(
    client: PoolClient,
    userId: any,
    requestDetails: RequestMetadata
  ): Promise<boolean> {
    const { ip, userAgent } = requestDetails;
    const hasLoginDetails = await this.hasLoginDetails(client, userId);
    let res;
    const values = [0, null, userId];
    if (hasLoginDetails) {
      res = await client.query(
        `UPDATE users 
        SET  wrong_login_count = $1,
        last_wrong_login_attempt = $2 WHERE id = $3`,
        values
      );
    } else {
      res = await client.query(
        `INSERT INTO users  
        (wrong_login_count, last_wrong_login_attempt,id) 
        VALUES ($1,$2,$3)`,
        values
      );
    }
    await client.query(
      `INSERT INTO user_login_history
      (user_id, login_at, login_ip, login_type, user_agent, created_by) 
      VALUES ($1, $2, $3, $4, $5, $6)`,
      [userId, moment(), ip, 'NORMAL', userAgent, userId]
    );
    return res.rowCount === 1;
  }

  async markWrongLoginAttempt(
    client: PoolClient,
    wrongLoginCount: number,
    userId: Id
  ): Promise<boolean> {
    const hasLoginDetails = await this.hasLoginDetails(client, userId);
    let res;
    const values = [wrongLoginCount, moment(), userId];
    if (hasLoginDetails) {
      res = await client.query(
        `UPDATE users  
        SET wrong_login_count = $1, last_wrong_login_attempt = $2 
        WHERE id = $3`,
        values
      );
    } else {
      res = await client.query(
        `INSERT INTO users  
        (wrong_login_count, last_wrong_login_attempt, id) 
        VALUES ($1, $2, $3)`,
        values
      );
    }
    return res.rowCount === 1;
  }

  async hasLoginDetails(client: PoolClient, userId: Id): Promise<boolean> {
    const res = await client.query(
      `SELECT id  FROM users  
      WHERE id = $1`,
      [userId]
    );
    return Mapper.getId(res) !== 0;
  }

  async deleteUserById(client: PoolClient, id: Id): Promise<boolean> {
    const res = await client.query('DELETE FROM users WHERE id = $1', [id]);
    return res.rowCount === 1;
  }

  async attachRole(
    client: PoolClient,
    userId: number,
    role: string | null | undefined
  ): Promise<boolean> {
    const res = await client.query(
      `INSERT INTO user_roles (user_id, role_id)
      VALUES ($1,(SELECT id FROM roles WHERE name = $2))`,
      [userId, role]
    );
    return res.rowCount === 1;
  }

  async findDuplicate(
    client: PoolClient,
    user: UserDto,
    ignoreId?: Id
  ): Promise<boolean> {
    const qb = new QueryBuilder(
      `SELECT id FROM users 
      WHERE email = ?\n`,
      [user.email]
    );

    if (ignoreId) {
      qb.append('AND id != ?', [ignoreId]);
    }

    const { sql, args } = qb.build();
    const res = await client.query(sql, args);
    return Mapper.getId(res) !== 0;
  }

  async saveToken(
    client: PoolClient,
    token: string,
    userId: Id,
    requestMetadata: RequestMetadata
  ): Promise<MappedPasswordResetToken | null> {
    const query = `
      INSERT INTO password_reset_tokens
        (user_id, token, validity_seconds, request_ip, request_user_agent)
      VALUES
        ($1, $2, $3, $4, $5)
      ON CONFLICT (user_id)
      DO UPDATE SET
        token = EXCLUDED.token,
        validity_seconds = EXCLUDED.validity_seconds,
        request_ip = EXCLUDED.request_ip,
        request_user_agent = EXCLUDED.request_user_agent,
        created_on = NOW()
      RETURNING *;
    `;

    const args = [
      userId,
      token,
      UserDao.MAX_TOKEN_VALIDITY,
      requestMetadata.ip,
      requestMetadata.userAgent,
    ];
    const res = await client.query(query, args);
    return Mapper.getUnique(res, UserDao.mapPasswordResetToken);
  }

  async findUserDetailsByToken(client: PoolClient, token: string) {
    const res = await client.query(
      `SELECT u.email,prt.user_id,prt.created_on,prt.validity_seconds, ud.first_name, ud.last_name 
        FROM password_reset_tokens prt
        LEFT JOIN user_details ud on ud.user_id = prt.user_id 
        LEFT JOIN users u on u.id = ud.user_id
        WHERE prt.token = $1`,
      [token]
    );
    return UserDao.mapUserDetailsByToken(res.rows);
  }

  async deleteToken(client: PoolClient, token: string) {
    const res = await client.query(
      'DELETE FROM password_reset_tokens WHERE token = $1',
      [token]
    );
    return res.rowCount === 1;
  }

  async logPasswordReset(
    client: PoolClient,
    data: LogPasswordResetDto
  ): Promise<boolean> {
    const {
      userId,
      token,
      userAgent,
      ip,
      createdBy,
      actionedBy,
      actionByType,
    } = data;

    const query = `
    INSERT INTO password_reset_history (
      user_id,
      token,
      actioned_by,
      action_by_type,
      action_user_user_agent,
      action_user_ip,
      created_by
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7
    )
    ON CONFLICT (user_id)
    DO UPDATE SET
      actioned_by = EXCLUDED.actioned_by,
      action_by_type = EXCLUDED.action_by_type,
      action_user_user_agent = EXCLUDED.action_user_user_agent,
      action_user_ip = EXCLUDED.action_user_ip,
      created_by = EXCLUDED.created_by,
      created_on = NOW()
    RETURNING *;
  `;

    const args = [
      userId,
      token,
      actionedBy,
      actionByType,
      userAgent,
      ip,
      createdBy,
    ];
    const res = await client.query(query, args);
    return res.rowCount === 1;
  }

  async uploadProfilePicture(
    client: PoolClient,
    key: string,
    actionUser: CurrentUser
  ) {
    const res = await client.query(
      'UPDATE user_details set profile_picture_path = $1 WHERE user_id = $2',
      [key, actionUser.id]
    );
    return res.rowCount === 1;
  }

  async deleteProfilePicture(
    client: PoolClient,
    actionUser: CurrentUser,
    userId: Id
  ) {
    const strQuery =
      'UPDATE user_details SET profile_picture_path  = $1, updated_by = $2 WHERE user_id = $3';
    const args = [null, actionUser.id, userId];
    const res = await client.query(strQuery, args);
    return res.rowCount === 1;
  }

  static mapUserWithRoles = (row: Record<string, any>): MappedUser => {
    return {
      id: parserId(row.id),
      school: row.school || null,
      email: row.email,
      passwordHash: row.password ? new PasswordHash(row.password) : null,
      status: row.status,
      firstName: row.first_name,
      lastName: row.last_name,
      name: `${row.first_name} ${row.last_name}`,
      wrongLoginCount: parserInteger(row.wrong_login_count),
      lastWrongLoginAttempt: parserDate(row.last_wrong_login_attempt),
      lastLogin: parserDate(row?.last_login_at),
      createdOn: parserDate(row.created_on),
      roleIds: row.role_ids,
      role: row.role,
      profilePicture: row.profile_picture || null,
    };
  };

  static mapPasswordResetToken = (
    row: Record<string, any>
  ): MappedPasswordResetToken => {
    return {
      userId: parserId(row.user_id),
      token: row.token,
      createdOn: parserDate(row.created_on),
      validitySeconds: parserInteger(row.validity_seconds),
    };
  };

  static mapUserDetailsByToken(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      email: firstRow.email,
      userId: parserId(firstRow.user_id),
      firstName: firstRow.first_name,
      lastName: firstRow.last_name,
      createdOn: parserDate(firstRow.created_on),
      validitySecs: parserInteger(firstRow.validity_seconds),
    };
  }
}

export default UserDao;
