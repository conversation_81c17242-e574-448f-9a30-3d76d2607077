import { Container } from 'typedi';
import UserDao from './userDao.ts';
import {
  HttpException,
  STATUS,
  formatErrorResponse,
  formatSuccessResponse,
  generateUUIDString,
  messageResponse,
  parserString,
  handleError,
  commonCatchHandler,
  randomPasswordGenerator,
  ROLE,
  PORTAL,
} from '../../utils/index.ts';
import { Password } from '../../models/index.ts';
import { parserId, parserInteger } from '../../utils/daoHelper/index.ts';
import type { PoolClient } from 'pg';
import type { RequestMetadata, UserDto } from '../security/types.ts';
import type {
  ChangePasswordDto,
  LogPasswordResetDto,
  MappedPasswordResetToken,
  MappedUser,
  ResetPasswordDto,
  UpdatePasswordDto,
  UpdateProfileDto,
  ValidatePasswordDto,
} from './types.ts';
import type { CurrentUser, Id } from '../../models/genericTypes.ts';
import { MessageService, BaseService } from '../../externalServices/index.ts';
import Role, { RoleName } from '../../auth/role.ts';
import { Portal, User } from '../../externalServices/messageService.ts';

class UserService extends BaseService {
  static MAX_TOKEN_GENERATION_ATTEMPTS = 5;

  private dao: UserDao;
  constructor() {
    super();
    this.dao = Container.get(UserDao);
  }

  async createUser(
    client: PoolClient,
    dto: UserDto,
    isStaff = true,
    createdBy?: number
  ): Promise<MappedUser> {
    const messageKey = 'createUser';
    if (await this.dao.findDuplicate(client, dto)) {
      throw new HttpException.Conflict(
        formatErrorResponse(messageKey, 'duplicateUser')
      );
    }
    try {
      const roles = Array.from(new Set(dto.role ?? []));
      if (isStaff && roles.length && !Role.isStaffRoles(roles)) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'invalidStaffRole')
        );
      }
      let password;
      let actualPassword;
      if (dto.password) {
        password = dto.password;
      } else {
        const generated = randomPasswordGenerator(8);
        actualPassword = generated.string;
        password = generated.md5String;
      }
      const userDto = await UserService.createUserDto({
        ...dto,
        password,
      });
      const id = await this.dao.createUser(
        client,
        userDto,
        createdBy || 0 // For signup
      );
      await Promise.all(
        roles.map(async (role) => {
          await this.dao.attachRole(client, id, role);
        })
      );
      const user = await this.findUserById(client, id);
      if (!user) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'unableToCreate')
        );
      }

      const portal = UserService.getPortal(user.role);
      if (dto.notifyUser) {
        await this.sendAddUserConfirmationEmail(
          { ...user, actualPassword },
          portal
        );
      }

      return user;
    } catch (err: Error | any) {
      console.log(err);
      throw commonCatchHandler(err, messageKey);
    }
  }

  async updateRoleByUserId(
    client: PoolClient,
    role: RoleName[],
    userId: number
  ) {
    const messageKey = 'updateRole';
    try {
      const isupdated = await this.dao.updateRoleByUserId(client, role, userId);
      if (!isupdated) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'unableToUpdate')
        );
      }
      return true;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async sendCredential(
    client: PoolClient,
    dto: MappedUser,
    actionUser: CurrentUser
  ) {
    const messageKey = 'sendCredentials';
    try {
      const { newPassword, salt, actualPassword } =
        await UserService.generatePassword();

      const isUpdated = await this.updatePassword(
        client,
        {
          id: dto.id,
          salt,
          newPassword,
          updatedBy: actionUser.id,
        },
        messageKey
      );
      if (!isUpdated) {
        throw new HttpException.ServerError(
          formatErrorResponse(messageKey, 'unableToSend')
        );
      }
      await this.sendPasswordChangeEmail({ ...dto, actualPassword });
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async makeSchoolUser(
    client: PoolClient,
    dto: {
      userId: Id;
      schoolId: Id;
      isOwnwer: boolean;
    }
  ): Promise<boolean> {
    const messageKey = 'makeSchoolUser';
    try {
      if (!dto?.userId || !dto?.schoolId) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'unableToUpdate')
        );
      }
      const success = await this.dao.makeSchoolUser(client, dto);
      if (!success) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'unableToUpdate')
        );
      }
      return true;
    } catch (err: any) {
      throw commonCatchHandler(err, messageKey);
    }
  }

  async getRights(actionUser: CurrentUser): Promise<string[]> {
    const { rights } = actionUser;
    return rights;
  }

  async updateUser(
    client: PoolClient,
    dto: UserDto,
    updatedBy: Id
  ): Promise<MappedUser | null> {
    const messageKey = 'updateUser';
    try {
      if (!dto?.id) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToUpdate')
        );
      }
      const updateUserDto = UserService.updateUserDto(dto, updatedBy);
      const success = await this.dao.updateUser(client, updateUserDto);
      if (!success)
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'unableToUpdate')
        );
      return await this.findUserById(client, dto?.id);
    } catch (err) {
      console.log(err);
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'unableToUpdate')
      );
    }
  }

  async markUserLogin(
    client: PoolClient,
    userId: Id,
    requestDetails: RequestMetadata
  ) {
    const messageKey = 'markUserLogin';
    try {
      await this.dao.markUserLogin(client, userId, requestDetails);
    } catch (error) {
      console.log(error);
      throw new HttpException.ServerError(
        formatErrorResponse(messageKey, 'unableToMark')
      );
    }
  }

  async updateUserWrongLoginCount(wrongLoginCount: number, userId: Id) {
    return this.txs.withTransaction(async (client) => {
      await this.dao.markWrongLoginAttempt(client, wrongLoginCount, userId);
    });
  }

  async findUserById(client: PoolClient, id: Id): Promise<MappedUser | null> {
    return UserService.fromUser(await this.dao.findUserById(client, id));
  }

  async findUserByEmail(client: PoolClient, email: string) {
    return UserService.fromUser(await this.dao.findUserByEmail(client, email));
  }

  async findPersistedUserById(id: Id, messageKey = 'fetchUser') {
    return this.txs.withTransaction(async (client) => {
      const user = await this.findUserById(client, id);
      if (!user) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'notFound')
        );
      }
      return user;
    });
  }

  async fetchUserProfile(actionUser: CurrentUser) {
    return UserService.fromUserProfile(actionUser);
  }

  async modifyUserProfile(
    updateDto: UpdateProfileDto,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const dto = UserService.updateProfileDto(
        updateDto as UserDto,
        actionUser.id
      );
      const user = await this.updateUser(client, dto, actionUser.id);
      return (
        user &&
        messageResponse(
          formatSuccessResponse('profile', 'updatedSucessfully'),
          UserService.fromUserProfile(user)
        )
      );
    });
  }

  async changePassword(dto: ChangePasswordDto, actionUser: CurrentUser) {
    const messageKey = 'changePassword';
    return this.txs.withTransaction(async (client) => {
      try {
        await this.isOldPasswordValid(
          client,
          dto.oldPassword,
          actionUser,
          messageKey
        );

        UserService.validatePassword(dto, messageKey, true);

        const { hash, salt } = await new Password(
          dto.newPassword
        ).hashPassword();

        const isUpdated = await this.updatePassword(
          client,
          {
            id: actionUser.id,
            salt,
            newPassword: hash,
            updatedBy: actionUser.id,
          },
          messageKey
        );
        if (!isUpdated) {
          throw new HttpException.ServerError(
            formatErrorResponse(messageKey, 'unableToUpdate')
          );
        }

        return messageResponse(
          formatSuccessResponse(messageKey, 'updatedSucessfully')
        );
      } catch (error: any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async isOldPasswordValid(
    client: PoolClient,
    oldPassword: string,
    actionUser: { id: Id },
    messageKey: string
  ) {
    const user = await this.dao.findUserById(client, actionUser.id);
    const validPassword = await user?.passwordHash?.check(oldPassword);
    if (!validPassword) {
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'invalidOldPassword')
      );
    }
  }

  async updatePassword(
    client: PoolClient,
    dto: UpdatePasswordDto,
    messageKey: string
  ) {
    try {
      const isUpdated = await this.dao.updatePassword(client, dto);
      if (!isUpdated) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToChangePassword')
        );
      }
      return messageResponse(
        formatSuccessResponse(messageKey, 'passwordChanged')
      );
    } catch (error: any) {
      return handleError(error, messageKey, 'unableToChangePassword');
    }
  }

  async forgotPassword(email: string, requestMetadata: RequestMetadata) {
    const messageKey = 'forgotPassword';
    const success = formatSuccessResponse(messageKey, 'emailSent');
    try {
      return this.txs.withTransaction(async (client) => {
        const userResult = await this.dao.findUserByEmail(client, email, true);
        const user = userResult === undefined ? null : userResult;
        this.isValidUser(user, messageKey);
        const token = await this.getTokenResult(
          client,
          user,
          requestMetadata,
          messageKey
        );

        if (!userResult) {
          throw new HttpException.NotFound(
            formatErrorResponse('user', 'notFound')
          );
        }

        if (token) {
          const portal = UserService.getPortal(userResult.role);
          await this.sendForgetPasswordEmail(user, token, portal);
        }
        return messageResponse(success);
      });
    } catch (error: any) {
      return handleError(
        error,
        messageKey,
        'failedToGeneratePasswordResetLink'
      );
    }
  }

  isValidUser(
    user: MappedUser | null | undefined,
    messageKey: string
  ): asserts user is MappedUser {
    if (!user) {
      throw new HttpException.NotFound(
        formatErrorResponse(messageKey, 'notFound')
      );
    }
  }

  async getTokenResult(
    client: PoolClient,
    user: MappedUser | null,
    requestMetadata: RequestMetadata,
    messageKey: string
  ): Promise<MappedPasswordResetToken | null> {
    const userId = user?.id;

    if (!userId) {
      throw new HttpException.NotFound(
        formatErrorResponse(messageKey, 'notFound')
      );
    }
    const newToken = await this.generateUUIDToken(
      client,
      userId,
      requestMetadata,
      messageKey
    );
    return newToken;
  }

  async generateUUIDToken(
    client: PoolClient,
    userId: number,
    requestMetadata: RequestMetadata,
    messageKey: string
  ): Promise<MappedPasswordResetToken | null> {
    let success = false;
    let attempts = 0;
    const maxAttempts = 5;
    /**
     * @type {import('./userDao').PasswordRestToken}
     */
    let tokenResult: MappedPasswordResetToken | null = null;

    while (!success && attempts < maxAttempts) {
      try {
        const token = generateUUIDString();
        // eslint-disable-next-line no-await-in-loop
        tokenResult = await this.dao.saveToken(
          client,
          token,
          userId,
          requestMetadata
        );
        success = tokenResult?.token === token;
      } catch (error: any) {
        if (error?.code !== '23505') {
          break;
        }
      }
      attempts += 1;
    }

    if (!success) {
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'unableToGeneratePasswordResetLink')
      );
    }
    return tokenResult;
  }

  async sendAddUserConfirmationEmail(user: User, portal: Portal) {
    const isMailSent = await MessageService.sendAddUserConfirmationEmail(
      user,
      portal
    );
    if (!isMailSent) {
      throw new HttpException.ServerError(
        formatErrorResponse('forgetPassword', 'unableToSendLoginLink')
      );
    }
  }

  async sendForgetPasswordEmail(
    user: MappedUser,
    token: MappedPasswordResetToken,
    portal: Portal
  ) {
    const isMailSent = await MessageService.sendPasswordReset(
      user,
      token,
      portal
    );
    if (!isMailSent) {
      throw new HttpException.ServerError(
        formatErrorResponse('forgetPassword', 'unableToSendPasswordResetLink')
      );
    }
  }

  async sendPasswordChangeEmail(user: User) {
    const isMailSent = await MessageService.sendPasswordChangeEmail(user);
    if (!isMailSent) {
      throw new HttpException.ServerError(
        formatErrorResponse('forgetPassword', 'unableToSendPasswordChangeEmail')
      );
    }
  }

  async sendResetPasswordEmail(user: MappedUser, portal: Portal) {
    const isMailSent = await MessageService.sendPasswordResetConfirmation(
      user,
      portal
    );
    if (!isMailSent) {
      throw new HttpException.ServerError(
        formatErrorResponse('forgetPassword', 'unableToSendPasswordResetLink')
      );
    }
  }

  async findUserDetailsByToken(client: PoolClient, token: string) {
    return await this.dao.findUserDetailsByToken(client, token);
  }

  async deleteToken(client: PoolClient, token: string) {
    return await this.dao.deleteToken(client, token);
  }

  async logPasswordReset(client: PoolClient, dto: LogPasswordResetDto) {
    return await this.dao.logPasswordReset(client, dto);
  }

  // async uploadProfileImage(
  //   client: PoolClient,
  //   files: MulterFiles,
  //   actionUser: CurrentUser
  // ) {
  //   const messageKey = 'profile';
  //   if (!files) {
  //     throw new HttpException.BadRequest(
  //       formatErrorResponse(messageKey, 'filesNotProvided')
  //     );
  //   }
  //   if (!Array.isArray(files)) {
  //     console.log('messageKey', files);
  //     throw new HttpException.BadRequest(
  //       formatErrorResponse(messageKey, 'invalidData')
  //     );
  //   }
  //   if (files.length === 0) {
  //     throw new HttpException.BadRequest(
  //       formatErrorResponse(messageKey, 'noFilesProvided')
  //     );
  //   }
  //   if (files.length > 1) {
  //     try {
  //       // eslint-disable-next-line
  //       for (const file of files) {
  //         // eslint-disable-next-line no-await-in-loop
  //         await deleteFile(file.path);
  //       }
  //     } catch (error) {
  //       console.log(
  //         '[ERROR][USERS] Unable to delete files (more than one is uploaded)',
  //         error
  //       );
  //     }
  //     throw new HttpException.BadRequest(
  //       formatErrorResponse(messageKey, 'onlyOneFileAllowed')
  //     );
  //   }
  //   try {
  //     const file = files[0];
  //     const photoUploadResponse = await S3Service.uploadToS3(
  //       file,
  //       S3Service.PROFILE_PICTURE
  //     );
  //     const s3Key = photoUploadResponse.key;
  //     await this.dao.uploadProfilePicture(client, s3Key, actionUser);
  //     return messageResponse(
  //       formatSuccessResponse(messageKey, 'profilePictureUploadedSuccessFully')
  //     );
  //   } catch (error: Error | any) {
  //     console.log('[ERROR][USERS] Unable to upload photo', error);
  //     throw commonCatchHandler(error, messageKey);
  //   }
  // }

  // async uploadImages(files: MulterFiles, actionUser: CurrentUser) {
  //   return await this.txs.withTransaction(
  //     async (client) => await this.uploadProfileImage(client, files, actionUser)
  //   );
  // }

  // async removeProfilePicture(actionUser: CurrentUser) {
  //   return await this.txs.withTransaction(
  //     async (client) => await this.deleteProfilePicture(client, actionUser)
  //   );
  // }

  // async deleteProfilePicture(client: PoolClient, actionUser: CurrentUser) {
  //   const messageKey: string = 'profile';
  //   try {
  //     const res = await this.fetchUserProfile(actionUser);
  //     if (!res?.profilePicture) {
  //       throw new HttpException.NotFound(
  //         formatErrorResponse('profile', 'notFound')
  //       );
  //     }

  //     const isDeleted = await this.dao.deleteProfilePicture(
  //       client,
  //       actionUser,
  //       actionUser.id
  //     );
  //     if (!isDeleted) {
  //       throw new HttpException.ServerError(
  //         formatErrorResponse('profile', 'imageUnableToRemove')
  //       );
  //     }
  //     await S3Service.removeFromS3(res.profilePicture);

  //     return messageResponse(
  //       formatSuccessResponse('profile', 'imageRemovedSuccessfully')
  //     );
  //   } catch (e: any) {
  //     throw commonCatchHandler(e, messageKey);
  //   }
  // }

  static getPortal(role: RoleName[]) {
    return role.includes(ROLE.SUPER_ADMIN)
      ? PORTAL.ADMIN_PORTAL
      : PORTAL.STUDENT_PORTAL;
  }

  static validatePassword(
    dto: ValidatePasswordDto,
    messageKey: string,
    blockSameOldPassword = false
  ) {
    if (dto?.newPassword !== dto?.confirmPassword) {
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'passwordsDoNotMatch')
      );
    }

    if (blockSameOldPassword && dto?.oldPassword === dto?.newPassword) {
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'oldPasswordSameAsNew')
      );
    }
  }

  static async generatePassword() {
    let password;
    let actualPassword;

    const generated = randomPasswordGenerator(8);
    actualPassword = generated.string;
    password = generated.md5String;
    const res = await new Password(password).hashPassword();

    return { newPassword: res.hash, salt: res.salt, actualPassword };
  }

  static async createUserDto(dto: UserDto) {
    let hash: string | null = null;
    let salt: string | undefined = undefined;

    if (dto.password) {
      const res = await new Password(dto.password).hashPassword();
      hash = res.hash;
      salt = res.salt;
    }

    if (!hash || !salt) {
      throw new HttpException.BadRequest(
        formatErrorResponse('createUser', 'invalidCredentials')
      );
    }

    return {
      firstName: dto.firstName,
      lastName: dto.lastName,
      email: dto.email,
      password: hash,
      salt: salt,
      status: dto.status ?? STATUS.ACTIVE,
    };
  }

  static updateUserDto(dto: UserDto, updatedBy: Id) {
    return {
      id: parserId(dto.id),
      firstName: parserString(dto?.firstName),
      lastName: parserString(dto?.lastName),
      email: parserString(dto?.email),
      status: parserString(dto.status),
      updatedBy,
    };
  }

  static updateProfileDto(dto: UserDto, actionUserId: Id) {
    return {
      id: parserId(actionUserId),
      firstName: parserString(dto?.firstName),
      lastName: parserString(dto?.lastName),
      email: parserString(dto?.email),
    };
  }

  static fromUser(user: MappedUser | null) {
    if (!user) {
      return null;
    }

    return {
      ...user,
    };
  }

  static fromUserProfile(user: MappedUser | CurrentUser) {
    if (!user) {
      return null;
    }

    return {
      id: user.id,
      school: user?.school || null,
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      status: user.status,
      role: user.role,
      profilePicture: user.profilePicture || null,
      lastLogin: user.lastLogin,
    };
  }

  static changePasswordDto(dto: ChangePasswordDto, actionUserId: Id) {
    return {
      newPassword: parserString(dto?.newPassword),
      oldPassword: parserString(dto?.oldPassword),
      confirmPassword: parserString(dto?.confirmPassword),
      id: parserInteger(actionUserId),
    };
  }

  static requestForgotPasswordDto(dto: { email: string }) {
    return {
      email: parserString(dto?.email),
    };
  }

  static resetPasswordDto(dto: ResetPasswordDto, userId: Id) {
    return {
      id: parserInteger(userId),
      resetToken: parserString(dto?.email),
      newPassword: parserString(dto?.newPassword),
      confirmPassword: parserString(dto?.confirmPassword),
    };
  }
}

export default UserService;
