import type { Id } from '../../../models/genericTypes.ts';
import { filterUndefinedFromObject } from '../../../utils/index.ts';

type FilterObj = {
  firstName?: string;
  lastName?: string;
  updatedBy: Id;
};

export default (user : FilterObj) => {
  const map = {
    first_name: user?.firstName,
    last_name: user?.lastName,
    updated_by: user?.updatedBy,
  };

  return filterUndefinedFromObject(map);
};
