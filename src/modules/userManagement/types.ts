import type { Moment } from 'moment';
import type { RoleName } from '../../auth/role.ts';
import PasswordHash from '../../models/passwordHash.ts';
import { Id } from '../../models/genericTypes.ts';
import { ACTION_BY_TYPE } from '../../utils/constants.ts';
import { PermissionName } from '../../auth/permission.ts';

export type UserRow = {
  id: Id;
  email: string;
  password?: string | null;
  status: string;
  first_name: string;
  last_name: string;
  wrong_login_count?: number | string | null;
  last_wrong_login_attempt?: string | Date | null;
  last_login?: string | Date | null;
  created_on: string | Date;
  role: any;
  profile_picture?: string | null;
};

export type MappedUser = {
  id: Id;
  email: string;
  passwordHash: PasswordHash | null;
  status: string;
  firstName: string;
  lastName: string;
  name: string;
  wrongLoginCount: number | null;
  lastWrongLoginAttempt: Moment | null;
  lastLogin: Moment | null;
  createdOn: Moment | null;
  roleIds?: number[];
  role: RoleName[];
  rolePermissions?: PermissionName[];
  userPermissions?: PermissionName[];
  profilePicture?: string | null;
  school?: any | null;
};

export type PasswordResetRow = {
  user_id: Id;
  token: string;
  created_on: string | Date | null;
  validity_seconds: number | string | null;
};

export type MappedPasswordResetToken = {
  userId: Id;
  token: string;
  createdOn: Moment | null;
  validitySeconds: number | null;
};

export type ResetPasswordDto = {
  id?: Id;
  email?: String;
  resetToken: string;
  newPassword: string;
  confirmPassword: string;
  oldPassword?: string;
};

export type ChangePasswordDto = {
  oldPassword: string;
  newPassword: string;
  confirmPassword: string;
};

export type UpdatePasswordDto = {
  id: Id;
  salt: string;
  newPassword: string;
  updatedBy: Id;
};

export type ValidatePasswordDto = {
  oldPassword?: string | null;
  newPassword: string | null;
  confirmPassword: string | null;
};

export type LogPasswordResetDto = {
  userId: Id;
  actionedBy: Id;
  actionByType: keyof typeof ACTION_BY_TYPE;
  token: string;
  userAgent: string;
  ip: string;
  createdBy: Id;
};

export type UpdateProfileDto = {
  firstName?: string;
  lastName?: string;
  email?: string;
};
