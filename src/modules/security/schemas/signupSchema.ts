import Joi from 'joi';
import {
  requiredStringValidator,
  requiredEmailValidator,
  requiredEnumValidator,
  getEnumArrayFromObj,
  SCHOOL_TYPE,
  nullableStringValidator,
} from '../../../utils/index.ts';

export default Joi.object(
  ((messageKey) => ({
    // School
    schoolName: requiredStringValidator(messageKey, 'schoolName'),
    schoolCode: requiredStringValidator(messageKey, 'schoolCode'),
    schoolPostalCode: nullableStringValidator(messageKey, 'schoolPostCode'),
    city: nullableStringValidator(messageKey, 'city'),
    schoolType: requiredEnumValidator(
      getEnumArrayFromObj(SCHOOL_TYPE) || [],
      messageKey,
      'schoolType'
    ),

    // School Admin
    firstName: requiredStringValidator(messageKey, 'firstName'),
    lastName: requiredStringValidator(messageKey, 'lastName'),
    email: requiredEmailValidator(messageKey, 'email'),
    password: requiredStringValidator(messageKey, 'password'),
  }))('signup')
).options({ stripUnknown: true });
