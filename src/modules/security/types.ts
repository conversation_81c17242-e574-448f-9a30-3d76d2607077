import type { Id } from '../../models/genericTypes.ts';
import { LOGIN_TOKEN_TYPE } from '../../utils/constants.ts';
import { RoleName } from '../../auth/role.ts';
import { SchoolSignupDto } from '../schoolManagement/types.ts';

export type RequestMetadata = {
  ip: string;
  userAgent: string;
};

export type UserDto = {
  firstName?: string | null;
  lastName?: string | null;
  email?: string | null;
  password?: string;
  salt?: string;
  role?: RoleName[];
  status?: string;
  id?: Id;
  notifyUser?: boolean;
};

export type VerifyTokenDto = {
  email: string;
  password: string;
  tokenType: keyof typeof LOGIN_TOKEN_TYPE;
  token: string;
};

export type ResetPasswordDto = {
  token: string;
  password: string;
  confirmPassword: string;
};

export type SignUpDto = SchoolSignupDto & UserDto;
