import { Container } from 'typedi';
import {
  routes,
  featureLevel,
  schoolPost,
  schoolPatch,
  schoolDelete,
  schoolGet,
} from '../../utils/index.ts';
import { Right } from '../../auth/index.ts';
import StaffService from './staffService.ts';
import { CurrentUser } from '../../models/genericTypes.ts';
import {
  createStaffSchema,
  sendCredentialsInBulkSchema,
  updateStaffSchema,
} from './schemas/index.ts';
import { UserDto } from '../security/types.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import Filter from '../../models/filter.ts';

export default () => {
  schoolPost(
    featureLevel.production,
    Right.staffManagement.CREATE_STAFF,
    routes.staffManagement.CREATE_STAFF,
    async (req) => {
      const service = Container.get(StaffService);
      const dto = await createStaffSchema.validateAsync(req.body);
      return await service.createStaff(dto, req.currentUser as CurrentUser);
    }
  );

  // schoolPost(
  //   featureLevel.production,
  //   Right.staffManagement.CREATE_STAFF_IN_BULK,
  //   routes.staffManagement.CREATE_STAFF_IN_BULK,
  //   async (req) => {
  //     const service = Container.get(StaffService);
  //     return await service.createStaffInBulk( req.currentUser as CurrentUser);
  //   },
  //   [uploadFile]
  // );

  schoolPost(
    featureLevel.production,
    Right.staffManagement.SEND_CREDENTIALS_TO_STAFF_BY_ID,
    routes.staffManagement.SEND_CREDENTIALS_TO_STAFF_BY_ID,
    async (req) => {
      const service = Container.get(StaffService);
      const { id } = req.params;
      return await service.sendCredentialsToStaffById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPost(
    featureLevel.production,
    Right.staffManagement.SEND_CREDENTIALS_TO_STAFFS_IN_BULK,
    routes.staffManagement.SEND_CREDENTIALS_TO_STAFFS_IN_BULK,
    async (req) => {
      const service = Container.get(StaffService);
      const dto = await sendCredentialsInBulkSchema.validateAsync(req.body);
      return await service.sendCredentialsInBulk(
        dto.staffIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolGet(
    featureLevel.production,
    Right.staffManagement.GET_STAFFS_LIST,
    routes.staffManagement.GET_STAFFS_LIST,
    async (req) => {
      const filter = Filter.fromRequest(req, Filter.types.STAFFS_LIST);
      const service = Container.get(StaffService);
      return await service.getStaffsList(filter, req.currentUser as CurrentUser);
    }
  );

  // Also act as a delete for now by changing the status
  schoolPatch(
    featureLevel.production,
    Right.staffManagement.UPDATE_STAFF_BY_ID,
    routes.staffManagement.UPDATE_STAFF_BY_ID,
    async (req) => {
      const service = Container.get(StaffService);
      const { id } = req.params;
      const dto = await updateStaffSchema.validateAsync({ ...req.body });
      return await service.updateStaffById(
        { ...dto, id: parserId(id) } as UserDto,
        req.currentUser as CurrentUser
      );
    }
  );

  // schoolDelete(
  //   featureLevel.production,
  //   Right.staffManagement.DELETE_STAFF_BY_ID,
  //   routes.staffManagement.DELETE_STAFF_BY_ID,
  //   async (req) => {
  //     const service = Container.get(StaffService);
  //     return await service.deleteStaffById(req.currentUser as CurrentUser);
  //   }
  // );
};
