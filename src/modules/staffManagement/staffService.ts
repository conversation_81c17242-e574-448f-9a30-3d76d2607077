import { Container } from 'typedi';
import BaseService from '../../externalServices/baseService.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import { CreateStaffDto } from './types.ts';
import UserService from '../userManagement/userService.ts';
import { commonCatchHandler } from '../../utils/commonFunctions.ts';
import SchoolService from '../schoolManagement/schoolService.ts';
import {
  formatErrorResponse,
  formatSuccessResponse,
  HttpException,
  messageResponse,
} from '../../utils/index.ts';
import { UserDto } from '../security/types.ts';
import { PoolClient } from 'pg';
import { ParsedFilter } from '../../models/filter.ts';
import StaffDao from './staffDao.ts';

export default class StaffService extends BaseService {
  private dao: StaffDao;
  private userService: UserService;
  private schoolService: SchoolService;

  constructor() {
    super();
    this.dao = Container.get(StaffDao);
    this.userService = Container.get(UserService);
    this.schoolService = Container.get(SchoolService);
  }

  async sendCredentialsToStaff(
    client: PoolClient,
    schoolId: Id,
    id: Id,
    actionUser: CurrentUser
  ) {
    const [_, user] = await Promise.all([
      this.schoolService.isSchoolUser(client, schoolId, id),
      this.userService.findUserById(client, id),
    ]);
    if (!user) {
      throw new HttpException.NotFound(
        formatErrorResponse('staff', 'notFound')
      );
    }
    await this.userService.sendCredential(client, user, actionUser);
  }

  // Services
  async createStaff(dto: CreateStaffDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'createStaff';
      try {
        const schoolId = this.schoolService.checkSchoolIdIsPresent(
          actionUser.schoolId
        );
        const isStaff = true;
        const user = await this.userService.createUser(
          client,
          {
            ...dto,
            notifyUser: true,
          },
          isStaff,
          actionUser.id
        );
        await this.userService.makeSchoolUser(client, {
          userId: user.id,
          schoolId,
          isOwnwer: false,
        });
        return messageResponse(
          formatSuccessResponse(messageKey, 'createdSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async getStaffsList(filter: ParsedFilter, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'staffsList';
      try {
        const schoolId = this.schoolService.checkSchoolIdIsPresent(
          actionUser.schoolId
        );
        const res = await this.dao.getStaffsList(client, filter, schoolId);
        return res;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async sendCredentialsToStaffById(id: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'sendCredentials';
      try {
        const schoolId = this.schoolService.checkSchoolIdIsPresent(
          actionUser.schoolId
        );
        await this.sendCredentialsToStaff(client, schoolId, id, actionUser);
        return messageResponse(
          formatSuccessResponse(messageKey, 'sentSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async sendCredentialsInBulk(staffIds: number[], actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'sendCredentialsInBulk';
      try {
        const schoolId = this.schoolService.checkSchoolIdIsPresent(
          actionUser.schoolId
        );
        await Promise.all(
          staffIds.map((id) =>
            this.sendCredentialsToStaff(client, schoolId, id, actionUser)
          )
        );
        return messageResponse(
          formatSuccessResponse(messageKey, 'sentSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async updateStaffById(dto: UserDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'updateStaff';
      try {
        const schoolId = this.schoolService.checkSchoolIdIsPresent(
          actionUser.schoolId
        );
        if (!dto.id)
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'invalidUser')
          );

        await this.schoolService.isSchoolUser(client, schoolId, dto.id);

        if (dto.role && dto.role.length > 0) {
          await this.userService.updateRoleByUserId(client, dto.role, dto.id);
        }
        const user = await this.userService.updateUser(
          client,
          dto,
          actionUser.id
        );
        return (
          user &&
          messageResponse(
            formatSuccessResponse(messageKey, 'updatedSucessfully'),
            UserService.fromUserProfile(user)
          )
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }
}
