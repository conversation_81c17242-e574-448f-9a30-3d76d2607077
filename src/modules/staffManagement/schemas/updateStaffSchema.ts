import Joi from 'joi';
import {
  getEnumArrayFromObj,
  STATUS,
  nullableStringValidator,
  nullableEmailValidator,
  enumValidator,
  nullableArrayValidatorWithItems,
} from '../../../utils/index.ts';
import Role from '../../../auth/role.ts';

const allowedRoles = getEnumArrayFromObj(Role.staffRoleValues) || [];

export default Joi.object(
  ((messageKey) => ({
    firstName: nullableStringValidator(messageKey, 'firstName'),
    lastName: nullableStringValidator(messageKey, 'lastName'),
    email: nullableEmailValidator(messageKey, 'email'),
    role: nullableArrayValidatorWithItems(allowedRoles, messageKey, 'role'),
    status: enumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('updateStaff')
).options({ stripUnknown: true });
