import Joi from 'joi';
import {
  requiredStringValidator,
  requiredEmailValidator,
  requiredEnumValidator,
  getEnumArrayFromObj,
  STATUS,
  requiredArrayValidatorWithItems,
} from '../../../utils/index.ts';
import Role from '../../../auth/role.ts';

const allowedRoles = getEnumArrayFromObj(Role.staffRoleValues) || [];

export default Joi.object(
  ((messageKey) => ({
    firstName: requiredStringValidator(messageKey, 'firstName'),
    lastName: requiredStringValidator(messageKey, 'lastName'),
    email: requiredEmailValidator(messageKey, 'email'),
    role: requiredArrayValidatorWithItems(allowedRoles, messageKey, 'role'),
    status: requiredEnumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('createStaff')
).options({ stripUnknown: true });
