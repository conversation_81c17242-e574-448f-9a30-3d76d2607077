import { PoolClient } from 'pg';
import { ParsedFilter } from '../../models/filter.ts';
import { Id, Filter } from '../../models/genericTypes.ts';
import QueryBuilder from '../../utils/daoHelper/queryBuilder.ts';
import Joins from '../../utils/daoHelper/joins.ts';
import Mapper from '../../utils/daoHelper/mapper.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default class StaffsDao {
  async getStaffsList(client: PoolClient, filter: ParsedFilter, schoolId: Id) {
    const resultSelect = `
      SELECT
        u.id,
        ud.first_name AS firstName,
        ud.last_name AS lastName,
        u.email,
        u.status,
        last_login.login_at AS lastLoginAt,
        array_agg(DISTINCT cu.class_id) AS class_ids,
        array_agg(DISTINCT mgu.mentoring_group_id) AS mentor_ids,
        array_agg(DISTINCT r.id) AS role_ids,
        array_agg(DISTINCT r.name) AS role_names
    `;

    const fromAndJoins = `
      FROM users u
      JOIN school_users su ON su.user_id = u.id AND su.school_id = ?
      LEFT JOIN user_roles ur ON ur.user_id = u.id
      LEFT JOIN roles r ON r.id = ur.role_id
      LEFT JOIN user_details ud ON ud.user_id = u.id
      LEFT JOIN LATERAL (
        SELECT login_at
        FROM user_login_history ulh
        WHERE ulh.user_id = u.id
        ORDER BY login_at DESC
        LIMIT 1
      ) AS last_login ON true
      LEFT JOIN class_users cu ON cu.user_id = u.id
      LEFT JOIN mentoring_group_users mgu ON mgu.user_id = u.id
    `;

    const groupBy = `
      GROUP BY
        u.id, ud.first_name, ud.last_name, u.email, u.status, last_login.login_at
    `;

    const totalSelect = `SELECT COUNT(DISTINCT u.id) AS full_count `;

    const where: string[] = [];
    const args: any[] = [schoolId];
    const { filters } = filter;
    const search = filters.search ? `%${filters.search}%` : null;

    if (search) {
      where.push(
        `(u.email ILIKE ? OR ud.first_name ILIKE ? OR ud.last_name ILIKE ?)`
      );
      args.push(search, search, search);
    }
    if (filters.status) {
      where.push(`u.status = ?`);
      args.push(filters.status);
    }
    if (filters.roleId) {
      where.push(`ur.role_id = ?`);
      args.push(filters.roleId);
    }
    if (filters.classId) {
      where.push(`cu.class_id = ?`);
      args.push(filters.classId);
    }
    if (filters.mentorGroupId) {
      where.push(`mgu.mentoring_group_id = ?`);
      args.push(filters.mentorGroupId);
    }

    // Only allow TEACHER or MENTOR roles
    where.push(`ur.role_id IN (3, 4)`);

    const whereClause = where.length ? `WHERE ${where.join(' AND ')}` : '';

    const direction =
      filter.direction?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const orderField = this.getOrderField(filter, direction);

    args.push(filter.limit, filter.page * filter.limit);

    const resultQb = new QueryBuilder(
      `${resultSelect} ${fromAndJoins} ${whereClause} ${groupBy}`,
      args
    );
    resultQb.append(`ORDER BY ${orderField} LIMIT ? OFFSET ?`, []);

    const totalQb = new QueryBuilder(
      `${totalSelect} ${fromAndJoins} ${whereClause}`,
      args.slice(0, args.length - 2)
    );

    const { sql, args: outArgs } = resultQb.build();
    const { sql: countSql, args: countArgs } = totalQb.build();

    const rowsRes = await client.query(sql, outArgs);
    const totalRes = await client.query(countSql, countArgs);
    const total = Mapper.getTotalCount(totalRes);

    const results = Joins.resultMapper(rowsRes, StaffsDao.getStaffsListMapper);
    return Mapper.getPaginatedResponse(
      Mapper.getNewMetaData(filter as Filter, total),
      results
    );
  }

  private getOrderField(filter: ParsedFilter, direction: string): string {
    switch (filter.order) {
      case 'name':
        return `LOWER(CONCAT(ud.first_name, ' ', ud.last_name)) ASC`; // always ASC
      case 'email':
        return `LOWER(u.email) ${direction}`;
      case 'status':
        return `LOWER(u.status) ${direction}`;
      case 'lastLogin':
        return `last_login.login_at ${direction}`;
      default:
        return `u.id ${direction}`;
    }
  }

  static getStaffsListMapper(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      name: `${firstRow.firstname} ${firstRow.lastname}`,
      classIds: (firstRow.class_ids || [])
        .filter((id: any) => id != null)
        .map((id: any) => parserId(id)),
      mentorIds: (firstRow.mentor_ids || [])
        .filter((id: any) => id != null)
        .map((id: any) => parserId(id)),
      email: firstRow.email,
      role: (firstRow.role_ids || []).map((rid: any, idx: number) => ({
        id: parserId(rid),
        name: firstRow.role_names[idx],
      })),
      status: firstRow.status,
      lastLogin: firstRow.lastloginat,
    };
  }
}
