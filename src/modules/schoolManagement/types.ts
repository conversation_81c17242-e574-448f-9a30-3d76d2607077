import { SCHOOL_TYPE, STATUS, WEEK_DAYS } from '../../utils/constants.ts';

export type SchoolSignupDto = {
  schoolName: string;
  schoolCode: string;
  schoolPostalCode?: string | null;
  city?: string | null;
  schoolType: keyof typeof SCHOOL_TYPE;
  countryId?: number;
  schoolTypeId?: number;
};

export type School = {
  id: number;
  name: string;
  code: string;
  city: string;
  postalCode: string;
  status: keyof typeof STATUS;
  schoolType: keyof typeof SCHOOL_TYPE;
  countryName: string;
  weeklyCheckInCronEnabled: boolean;
  weeklyCheckInCronDay: keyof typeof WEEK_DAYS;
};
