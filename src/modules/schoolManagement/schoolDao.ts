import { PoolClient } from 'pg';
import { School, SchoolSignupDto } from './types.ts';
import { MappedUser } from '../userManagement/types.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import Joins from '../../utils/daoHelper/joins.ts';

export default class SchoolDao {
  // async getActiveSchoolIdsByUserId(
  //   client: PoolClient,
  //   userId: number
  // ): Promise<number[]> {
  //   const sql = `
  //   SELECT su.school_id
  //   FROM school_users su
  //   JOIN schools s ON s.id = su.school_id
  //   WHERE su.user_id = $1
  //     AND s.status = 'ACTIVE';
  // `;
  //   const result = await client.query(sql, [userId]);
  //   return result.rows.map((row) => parserId(row.school_id));
  // }

  async createSchool(
    client: PoolClient,
    dto: SchoolSignupDto,
    actionUser: MappedUser
  ) {
    const query = `
        INSERT INTO schools (name, code, school_type_id, country_id, city, postal_code, created_by, updated_by)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        RETURNING *;
        `;
    const values = [
      dto.schoolName,
      dto.schoolCode,
      dto.schoolTypeId,
      dto.countryId,
      dto.city,
      dto.schoolPostalCode,
      actionUser.id,
      actionUser.id,
    ];
    const result = await client.query(query, values);
    return result.rows[0];
  }

  async isSchoolUser(client: PoolClient, schoolId: number, userId: number) {
    const query =
      'SELECT * FROM school_users WHERE school_id = $1 AND user_id = $2';
    const result = await client.query(query, [schoolId, userId]);
    return result.rowCount === 1;
  }

  async getSchoolByNameAndCode(client: PoolClient, name: string, code: string) {
    const query = 'SELECT * FROM schools WHERE name = $1 AND code = $2';
    const result = await client.query(query, [name, code]);
    return result.rowCount === 1;
  }

  async getSchoolTypeByName(client: PoolClient, name: string) {
    const query = 'SELECT * FROM school_types WHERE name = $1';
    const result = await client.query(query, [name]);
    return result.rowCount === 1 ? result.rows[0] : null;
  }

  async getCountryByName(client: PoolClient, name: string) {
    const query = 'SELECT * FROM countries WHERE name = $1';
    const result = await client.query(query, [name]);
    return result.rowCount === 1 ? result.rows[0] : null;
  }

  async getShoolTypes(client: PoolClient) {
    const query = 'SELECT id, name, status FROM school_types;';
    const result = await client.query(query);
    if (!result.rows.length) return null;
    return Joins.resultMapper(result, SchoolDao.mapSchoolTypes);
  }

  static mapSchool(rows: Record<string, any>): School | null {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      name: firstRow.name,
      code: firstRow.code,
      city: firstRow.city,
      postalCode: firstRow.postal_code,
      status: firstRow.status,
      schoolType: firstRow.school_type,
      countryName: firstRow.country_name,
      weeklyCheckInCronEnabled: firstRow.weekly_check_in_cron_enabled,
      weeklyCheckInCronDay: firstRow.weekly_check_in_cron_day,
    };
  }

  static mapSchoolTypes(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      name: firstRow.name,
      status: firstRow.status,
    };
  }
}
