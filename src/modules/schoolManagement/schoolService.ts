import { Container } from 'typedi';
import { BaseService } from '../../externalServices/index.ts';
import SchoolDao from './schoolDao.ts';
import { School, SchoolSignupDto } from './types.ts';
import { commonCatchHandler } from '../../utils/commonFunctions.ts';
import {
  formatErrorResponse,
  HttpException,
  MAP_SCHOOL_TYPE,
  STATUS,
} from '../../utils/index.ts';
import { PoolClient } from 'pg';
import { MappedUser } from '../userManagement/types.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';

export default class SchoolService extends BaseService {
  private dao: SchoolDao;

  constructor() {
    super();
    this.dao = Container.get(SchoolDao);
  }

  static makeSchoolDto(dto: SchoolSignupDto) {
    return {
      ...dto,
      status: STATUS.ACTIVE,
      schoolPostalCode: dto.schoolPostalCode || null,
      city: dto.city || null,
    };
  }

  checkSchoolIdIsPresent(schoolId: Id | null | undefined) {
    if (!schoolId)
      throw new HttpException.BadRequest(
        formatErrorResponse('schoolId', 'required')
      );
    return schoolId;
  }

  // async getActiveSchoolIdsByUserId(client: PoolClient, id: number) {
  //   const messageKey = 'school';
  //   try {
  //     const schoolIds = await this.dao.getActiveSchoolIdsByUserId(client, id);
  //     if (!schoolIds.length) {
  //       throw new HttpException.BadRequest(
  //         formatErrorResponse(messageKey, 'inactive')
  //       );
  //     }
  //     return schoolIds;
  //   } catch (error: Error | any) {
  //     throw commonCatchHandler(error, messageKey);
  //   }
  // }

  // Return rights based on the school role permission
  // async validateSchool(
  //   schoolId: number,
  //   actionUser: CurrentUser
  // ): Promise<string[]> {
  //   return this.txs.withTransaction(async (client) => {
  //     const messageKey = 'validateschool';
  //     try {
  //       const isActiveSchool = actionUser.schoolIds?.includes(schoolId);
  //       if (isActiveSchool) return true;
  //       await this.isSchoolUser(client, schoolId, actionUser.id);
  //       // throw new HttpException.BadRequest(
  //       //   formatErrorResponse(messageKey, 'invalidSchoolId')
  //       // );
  //       return ['asdsa'];
  //     } catch (error: Error | any) {
  //       throw commonCatchHandler(error, messageKey);
  //     }
  //   });
  // }

  async isSchoolUser(client: PoolClient, schoolId: number, userId: number) {
    const messageKey = 'schoolUser';
    try {
      const schoolUser = await this.dao.isSchoolUser(client, schoolId, userId);
      if (!schoolUser) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'notFound')
        );
      }
      return true;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async getSchoolByNameAndCode(
    client: PoolClient,
    { name, code }: { name: string; code: string }
  ) {
    return await this.dao.getSchoolByNameAndCode(client, name, code);
  }

  async getSchoolTypeByName(client: PoolClient, name: string) {
    return await this.dao.getSchoolTypeByName(client, name);
  }

  async getCountryByName(client: PoolClient, name: string) {
    return await this.dao.getCountryByName(client, name);
  }

  // Services
  async createSchool(
    client: PoolClient,
    dto: SchoolSignupDto,
    actionUser: MappedUser
  ): Promise<School> {
    const messageKey = 'createSchool';
    try {
      const existingSchool = await this.getSchoolByNameAndCode(client, {
        name: dto.schoolName,
        code: dto.schoolCode,
      });
      if (existingSchool) {
        throw new HttpException.Conflict(
          formatErrorResponse(messageKey, 'schoolAlreadyExists')
        );
      }
      const schoolType = await this.getSchoolTypeByName(
        client,
        MAP_SCHOOL_TYPE[dto.schoolType]
      );
      if (!schoolType) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'invalidSchoolType')
        );
      }

      const country = await this.getCountryByName(client, 'Australia'); // Australia as default
      if (!country) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'invalidCountry')
        );
      }

      const schoolDto = SchoolService.makeSchoolDto({
        ...dto,
        schoolTypeId: schoolType.id,
        countryId: country.id,
      });
      const school = await this.dao.createSchool(client, schoolDto, actionUser);

      if (!school) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'schoolRegistrationFailed')
        );
      }
      return school;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async getShoolTypes() {
    return await this.txs.withTransaction(async (client) => {
      const messageKey = 'getSchoolTypes';
      try {
        const schoolTypes = await this.dao.getShoolTypes(client);
        return schoolTypes ?? [];
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }
}
