import { Container } from 'typedi';
import { routes, featureLevel, get } from '../../utils/index.ts';
import { Right } from '../../auth/index.ts';
import RolesService from './rolesService.js';
import { Filter } from '../../models/index.ts';

export default () => {
  get(
    featureLevel.production,
    Right.general.GET_ROLES,
    routes.general.GET_ROLES,
    async (req) => {
      const filter = Filter.fromRequest(req, Filter.types.ROLES);
      const service = Container.get(RolesService);
      return await service.findFilteredRoles(filter);
    }
  );
};
