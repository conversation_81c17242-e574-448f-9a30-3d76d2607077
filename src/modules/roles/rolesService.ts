import { Container } from 'typedi';
import RolesDao from './rolesDao.ts';
import { HttpException, formatErrorResponse } from '../../utils/index.ts';
import BaseService from '../../externalServices/baseService.ts';
import { ParsedFilter } from '../../models/filter.ts';
import { PoolClient } from 'pg';

class RolesService extends BaseService {
  private dao: RolesDao;

  constructor() {
    super();
    this.dao = Container.get(RolesDao);
  }

  async findFilteredRoles(filter: ParsedFilter) {
    return await this.txs.withTransaction(async (client) =>
      this.getRoles(client, filter)
    );
  }

  async getRoles(client: PoolClient, filter: ParsedFilter) {
    try {
      const res = await this.dao.getRoles(client, filter);
      // const { metadata, records } = res;
      // return { metadata, records: RolesService.mapRoles(records) };
      return res;
    } catch {
      throw new HttpException.BadRequest(
        formatErrorResponse('getRoles', 'unableToFetch')
      );
    }
  }

  // async getRoleByName(client, name) {
  //   try {
  //     const res = await this.dao.getRoleByName(client, name);
  //     return res;
  //   } catch (e) {
  //     console.log(e);
  //     throw new HttpException.BadRequest(
  //       formatErrorResponse('getRoleByName', 'unableToFetch')
  //     );
  //   }
  // }

  // static mapRoles(records: ) {
  //   const res = [];
  //   records.forEach((item) => {
  //     res.push({ ...item, name: RolesService.convertString(item.name) });
  //   });
  //   return res;
  // }

  static convertString(inputString: string) {
    return inputString
      .toLowerCase()
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }
}
export default RolesService;
