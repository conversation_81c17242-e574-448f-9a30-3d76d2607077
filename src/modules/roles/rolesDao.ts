import { PoolClient } from 'pg';
import { ParsedFilter } from '../../models/filter.ts';
import QueryBuilder from '../../utils/daoHelper/queryBuilder.ts';
import Mapper from '../../utils/daoHelper/mapper.ts';
import Joins from '../../utils/daoHelper/joins.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { Filter } from '../../models/genericTypes.ts';

class RolesDao {
  async getRoles(client: PoolClient, filter: ParsedFilter) {
    const resultSelect = 'SELECT r.id, r.name, r.type \n';
    const fromQuery = `FROM roles r 
    WHERE (
      r.name ILIKE ? 
    ) 
    \n`;
    const totalSelect = 'SELECT count(DISTINCT r.id) as full_count \n';
    const direction = filter.direction || 'desc';
    const orderQuery = `ORDER BY r.id ${direction} NULLS LAST\n`;
    let statusQuery = '';
    let typeQuery = '';
    const filters = { ...filter.filters };

    const { limit, page } = filter;
    const search = `%${filters.search || ''}%`;
    const searchArgs = [search];

    if (filters.type) {
      typeQuery = ' AND r.type = ? ';
      searchArgs.push(filters.type);
    }

    if (filters.status) {
      statusQuery = ' AND r.status = ? ';
      searchArgs.push(filters.status);
    }

    const resultQuery = resultSelect + fromQuery + typeQuery + statusQuery;
    const totalQuery = totalSelect + fromQuery + typeQuery + statusQuery;
    const resultQb = new QueryBuilder(resultQuery, searchArgs);
    const totalQb = new QueryBuilder(totalQuery, searchArgs);

    resultQb.append(orderQuery);
    resultQb.append('LIMIT ? OFFSET ?', [limit, page * limit]);
    const { sql, args } = resultQb.build();
    const { sql: totalSql, args: totalArgs } = totalQb.build();

    const res = await client.query(sql, args);
    const totalRes = await client.query(totalSql, totalArgs);
    const total = Mapper.getTotalCount(totalRes);
    const results = Joins.resultMapper(res, (rows) => rows[0]);

    return Mapper.getPaginatedResponse(
      Mapper.getNewMetaData(filter as Filter, total),
      results
    );
  }

  // async getRoleByName(client, name) {
  //   console.log(name);
  //   const qb = new QueryBuilder(
  //     'SELECT r.id, r.name, r.type, r.status, r.description FROM roles r WHERE r.name = ?',
  //     [name]
  //   );
  //   const { sql, args } = qb.build();
  //   const res = await client.query(sql, args);
  //   return RolesDao.getResult(res.rows);
  // }

  static getResult(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;

    return {
      id: parserId(firstRow.id),
      name: firstRow.name,
      type: firstRow.type,
      status: firstRow.status,
      description: firstRow.description,
    };
  }
}

export default RolesDao;
