import { PoolClient } from 'pg';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { Id } from '../../models/genericTypes.ts';
import { STATUS } from '../../utils/constants.ts';
import { Assessment } from './types.ts';
import Queries from '../../utils/daoHelper/queries.ts';

export default class SchoolAssessmentDao {
  async fetchAssessmentByCode(client: PoolClient, code: string) {
    const query = `
      SELECT a.id, a.code, a.name, a.title, a.description
      FROM assessments a
      WHERE code = $1
    `;
    const args = [code];
    const res = await client.query(query, args);

    return (
      (res.rowCount ?? 0) > 0 && SchoolAssessmentDao.mapAssessment(res.rows)
    );
  }

  async fetchQuestionsByAssessmentId(client: PoolClient, id: Id) {
    const res = await client.query(
      `
        SELECT id, display_configuration, visibility_configuration, code 
        FROM assessment_questions 
        WHERE assessment_id = $1
      `,
      [id]
    );

    return res.rows;
  }

  async fetchStaticElementsByAssessmentId(client: PoolClient, id: Id) {
    const res = await client.query(
      `
        SELECT id, element_type, display_configuration, visibility_configuration, code, status 
        FROM assessment_static_elements 
        WHERE assessment_id = $1 AND status = $2
      `,
      [id, STATUS.ACTIVE]
    );

    return res.rows;
  }

  async fetchStepsByAssessmentId(client: PoolClient, id: Id) {
    const res = await client.query(
      `
        SELECT 
          steps.id, 
          steps.display_configuration, 
          steps.display_order, 
          steps.display_type, 
          steps.navigation_type, 
          steps.visibility_configuration, 
          steps.code, 
          steps.status,
          COALESCE(ear.layout, '[]'::JSON) AS layout
        FROM 
          assessment_steps steps
        LEFT JOIN LATERAL (
          SELECT 
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', asl.child_id,
                'type', asl.child_type,
                'layout_configuration', asl.layout_configuration
              )
            ) AS layout
          FROM assessment_step_layouts asl
          WHERE asl.assessment_step_id = steps.id
        ) ear ON true
        WHERE steps.assessment_id = $1 
          AND steps.status = $2
      `,
      [id, STATUS.ACTIVE]
    );

    return res.rows;
  }

  async fetchSectionsByAssessmentId(client: PoolClient, id: Id) {
    const res = await client.query(
      `
        SELECT 
          ass.id, 
          ass.display_configuration, 
          ass.visibility_configuration, 
          ass.code, 
          ass.status,
          COALESCE(ear.layout, '[]'::JSON) AS layout
        FROM 
          assessment_step_sections ass
        LEFT JOIN LATERAL (
          SELECT 
            JSON_AGG(
              JSON_BUILD_OBJECT(
                'id', assl.child_id,
                'type', assl.child_type,
                'layout_configuration', assl.layout_configuration
              )
            ) AS layout
          FROM 
            assessment_step_section_layouts assl
          WHERE 
            assl.assessment_step_section_id = ass.id 
        ) ear ON true
        WHERE 
          ass.assessment_id = $1 
          AND ass.status = $2
      `,
      [id, STATUS.ACTIVE]
    );

    return res.rows;
  }

  async cloneAssessmentToSchool(
    client: PoolClient,
    dto: Assessment,
    createdBy: Id,
    schoolId: Id
  ) {
    const query = `INSERT INTO school_assessments (school_id, code, name, title, description, created_by, updated_by) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING id`;
    const args = [
      schoolId,
      dto.code,
      dto.name,
      dto.title,
      dto.description,
      createdBy,
      createdBy,
    ];
    const res = await client.query(query, args);
    if (!res.rowCount) return null;
    return res.rows[0].id;
  }

  async bulkInsertStaticElements<T extends { id: Id }>(
    client: PoolClient,
    staticElements: T[],
    dto: { clonedAssessmentId: Id; createdBy: Id }
  ) {
    if (!staticElements.length) return [];

    const insertQuery = `
      INSERT INTO school_assessment_static_elements (
        school_assessment_id,
        code,
        element_type,
        display_configuration,
        visibility_configuration,
        status,
        created_by
      )
    `;

    const { sql, args } = Queries.batchInsertWithReturningParameters(
      insertQuery,
      staticElements.map((staticElement: any) => ({
        school_assessment_id: dto.clonedAssessmentId,
        code: staticElement.code,
        element_type: staticElement.element_type,
        display_configuration: staticElement.display_configuration,
        visibility_configuration: staticElement.visibility_configuration,
        status: staticElement.status,
        created_by: dto.createdBy,
      })),
      (obj: any) => [
        obj.school_assessment_id,
        obj.code,
        obj.element_type,
        obj.display_configuration,
        obj.visibility_configuration,
        obj.status,
        obj.created_by,
      ],
      ['id']
    );

    const res = await client.query(sql, args);

    if (res.rowCount !== staticElements.length) return false;

    return res.rows.map((row, index) => ({
      newId: row.id,
      originalId: staticElements[index].id,
    }));
  }

  async bulkInsertSteps<T extends { id: Id }>(
    client: PoolClient,
    steps: T[],
    dto: { clonedAssessmentId: Id; createdBy: Id }
  ) {
    if (!steps.length) return [];

    const insertquery = `
      INSERT INTO school_assessment_steps (
        school_assessment_id,
        code,
        display_configuration,
        visibility_configuration,
        display_order,
        display_type,
        navigation_type,
        status,
        created_by
      )
    `;

    const { sql, args } = Queries.batchInsertWithReturningParameters(
      insertquery,
      steps.map((step: any) => ({
        schoolAssessmentId: dto.clonedAssessmentId,
        code: step.code,
        display_configuration: step.display_configuration,
        visibility_configuration: step.visibility_configuration,
        display_order: step.display_order,
        display_type: step.display_type,
        navigation_type: step.navigation_type,
        status: step.status,
        createdBy: dto.createdBy,
      })),
      (obj: any) => [
        obj.schoolAssessmentId,
        obj.code,
        obj.display_configuration,
        obj.visibility_configuration,
        obj.display_order,
        obj.display_type,
        obj.navigation_type,
        obj.status,
        obj.createdBy,
      ],
      ['id']
    );

    const res = await client.query(sql, args);

    return res.rows.map((row, index) => ({
      newId: row.id,
      originalId: steps[index].id,
    }));
  }

  async bulkInsertSections<T extends { id: Id }>(
    client: PoolClient,
    sections: T[],
    dto: { clonedAssessmentId: Id; createdBy: Id }
  ) {
    const insertquery = `
      INSERT INTO school_assessment_step_sections (
        cloned_assessment_id,
        code,
        display_configuration,
        visibility_configuration,
        status,
        created_by
      )
    `;

    const { sql, args } = Queries.batchInsertWithReturningParameters(
      insertquery,
      sections.map((section: any) => ({
        clonedAssessmentId: dto.clonedAssessmentId,
        code: section.code,
        display_configuration: section.display_configuration,
        visibility_configuration: section.visibility_configuration,
        status: section.status,
        createdBy: dto.createdBy,
      })),
      (obj: any) => [
        obj.clonedAssessmentId,
        obj.code,
        obj.display_configuration,
        obj.visibility_configuration,
        obj.status,
        obj.createdBy,
      ],
      ['id']
    );

    const res = await client.query(sql, args);

    return res.rows.map((row, index) => ({
      newId: row.id,
      originalId: sections[index].id,
    }));
  }

  static mapAssessment(rows: Record<string, any>) {
    const firstRow = rows[0];

    return {
      id: parserId(firstRow.id),
      code: firstRow.code,
      name: firstRow.name,
      title: firstRow.title ?? null,
      description: firstRow.description ?? null,
    };
  }
}
