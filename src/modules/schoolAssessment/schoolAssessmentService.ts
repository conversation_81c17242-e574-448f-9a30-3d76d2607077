import { Container } from 'typedi';
import BaseService from '../../externalServices/baseService.ts';
import SchoolAssessmentDao from './schoolAssessmentDao.ts';
import { PoolClient } from 'pg';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import {
  commonCatchHandler,
  formatErrorResponse,
  HttpException,
} from '../../utils/index.ts';
import { Assessment } from './types.ts';

export default class SchoolAssessmentService extends BaseService {
  private dao: SchoolAssessmentDao;

  constructor() {
    super();
    this.dao = Container.get(SchoolAssessmentDao);
  }

  async persistingAssessment(client: PoolClient, dto: { code: string }) {
    let messageKey = 'persistingAssessment';
    try {
      const { code } = dto;
      const assessment = await this.dao.fetchAssessmentByCode(client, code);

      if (!assessment) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, `assessmentWithCodeNotFound`)
        );
      }

      return assessment;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async generateAssessmentJSON(
    client: PoolClient,
    dto: Assessment,
    message: string
  ) {
    const messageKey = message || 'generateAssessmentJSON';
    try {
      let finalJSON = await this.formAssessmentJSON(client, dto, messageKey);

      return finalJSON;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async formAssessmentJSON(
    client: PoolClient,
    dto: Assessment,
    messageKey: string
  ) {
    try {
      const { id: assessmentId } = dto;
      let finalJSON: any = {};

      const [
        assessmentQuestions,
        assessmentStaticElements,
        assessmentSteps,
        assessmentSections,
      ] = await Promise.all([
        this.dao.fetchQuestionsByAssessmentId(client, assessmentId),
        this.dao.fetchStaticElementsByAssessmentId(client, assessmentId),
        this.dao.fetchStepsByAssessmentId(client, assessmentId),
        this.dao.fetchSectionsByAssessmentId(client, assessmentId),
      ]);

      finalJSON.code = dto.code;
      finalJSON.title = dto.title;
      finalJSON.description = dto.description;

      finalJSON.questions = assessmentQuestions;
      finalJSON.staticElements = assessmentStaticElements;
      finalJSON.steps = assessmentSteps;
      finalJSON.sections = assessmentSections;

      return finalJSON;
    } catch (error) {
      throw (
        error ||
        new HttpException.ServerError(
          formatErrorResponse(messageKey, 'serverError')
        )
      );
    }
  }

  // Service
  async cloneAssessmentToSchool(
    client: PoolClient,
    schoolId: Id,
    actionUser: CurrentUser
  ) {
    let messageKey = 'cloneAssessmentToSchool';
    try {
      const assessment = await this.persistingAssessment(client, {
        code: 'CHECK_IN_ASSESSEMNT',
      });

      const assessmentJSON = await this.generateAssessmentJSON(
        client,
        assessment,
        messageKey
      );

      const clonedAssessmentId = await this.dao.cloneAssessmentToSchool(
        client,
        assessment,
        actionUser.id,
        schoolId
      );

      if (!clonedAssessmentId) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'unableToCloneAssessment')
        );
      }

      const [staticElementIdsMap, stepIdsMap, sectionIdsMap, questionIdsMap] =
        await Promise.all([
          this.dao.bulkInsertStaticElements(
            client,
            assessmentJSON.staticElements,
            { clonedAssessmentId, createdBy: actionUser.id }
          ),
          this.dao.bulkInsertSteps(client, assessmentJSON.steps, {
            clonedAssessmentId,
            createdBy: actionUser.id,
          }),
          this.dao.bulkInsertSections(client, assessmentJSON.sections, {
            clonedAssessmentId,
            createdBy: actionUser.id,
          }),
          this.dao.bulkInsertQuestions(client, assessmentJSON.questions, {
            clonedAssessmentId,
            createdBy: actionUser.id,
          }),
        ]);

      const map = {
        STATIC_ELEMENT: staticElementIdsMap,
        SECTION: sectionIdsMap,
        STEP: stepIdsMap,
        QUESTION: questionIdsMap,
      };
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }
}
