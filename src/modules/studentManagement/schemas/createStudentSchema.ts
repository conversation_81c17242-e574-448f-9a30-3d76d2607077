import Joi from 'joi';
import {
  requiredStringValidator,
  requiredEnumValidator,
  getEnumArrayFromObj,
  STATUS,
  requiredEmailValidator,
  GENDER_TYPES,
  nullableIdValidator,
  nullableNumberArrayValidator,
  enumValidator,
} from '../../../utils/index.js';

export default Joi.object(
  ((messageKey) => ({
    firstName: requiredStringValidator(messageKey, 'firstName'),
    lastName: requiredStringValidator(messageKey, 'lastName'),
    email: requiredEmailValidator(messageKey, 'email'),
    classId: nullableIdValidator(messageKey, 'classId'),
    houseId: nullableIdValidator(messageKey, 'houseId'),
    mentorGroupIds: nullableNumberArrayValidator(messageKey, 'mentorGroupIds'),
    gender: enumValidator(
      getEnumArrayFromObj(GENDER_TYPES) || [],
      messageKey,
      'gender'
    ),
    status: requiredEnumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('createStudent')
).options({ stripUnknown: true });
