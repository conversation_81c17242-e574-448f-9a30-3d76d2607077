import Joi from 'joi';
import {
  getEnumArrayFromObj,
  STATUS,
  enumValidator,
  stringValidator,
  GENDER_TYPES,
  nullableIdValidator,
  nullableNumberArrayValidator,
  stringEmailValidator,
} from '../../../utils/index.js';

export default Joi.object(
  ((messageKey) => ({
    firstName: stringValidator(messageKey, 'firstName'),
    lastName: stringValidator(messageKey, 'lastName'),
    email: stringEmailValidator(messageKey, 'email'),
    classId: nullableIdValidator(messageKey, 'classId'),
    houseId: nullableIdValidator(messageKey, 'houseId'),
    mentorGroupIds: nullableNumberArrayValidator(messageKey, 'mentorGroupIds'),
    gender: enumValidator(
      getEnumArrayFromObj(GENDER_TYPES) || [],
      messageKey,
      'gender'
    ),
    status: enumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('updateStudent')
).options({ stripUnknown: true });
