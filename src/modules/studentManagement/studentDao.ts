import { PoolClient } from 'pg';
import { CreateStudentDto, UpdateStudentDto } from './types.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import Mapper from '../../utils/daoHelper/mapper.ts';
import Queries from '../../utils/daoHelper/queries.ts';
import studentUpdateMap from './updateMaps/studentUpdateMap.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default class StudentDao {
  async createStudent(
    client: PoolClient,
    dto: CreateStudentDto,
    actionUser: CurrentUser
  ) {}

  async updateStudent(client: PoolClient, dto: UpdateStudentDto) {
    const { sql, args } = Queries.updaterFor('students', studentUpdateMap, dto);
    const res = await client.query(sql, args);
    return res.rowCount === 1;
  }
}
