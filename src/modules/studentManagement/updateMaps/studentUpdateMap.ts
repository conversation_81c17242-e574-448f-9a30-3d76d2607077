import { filterUndefinedFromObject } from '../../../utils/index.js';
import { UpdateStudentDto } from '../types.js';

export default (classObj: UpdateStudentDto) => {
  const map = {
    id: classObj.id,
    firstName: classObj.firstName,
    lastName: classObj.lastName,
    email: classObj.email,
    gender: classObj.gender,
    classId: classObj.classId,
    houseId: classObj.houseId,
    status: classObj.status,
    updated_by: classObj.updatedBy,
  };

  return filterUndefinedFromObject(map);
};
