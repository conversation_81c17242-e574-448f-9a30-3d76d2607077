import { GENDER_TYPES, STATUS } from '../../utils/constants.ts';

export type CreateStudentDto = {
  firstName: string;
  lastName: string;
  email: string;
  gender?: keyof typeof GENDER_TYPES | null;
  classId?: number | null;
  houseId?: number | null;
  mentorGroupIds?: number[] | null;
  status: keyof typeof STATUS;
  schoolId: number;
};

export type UpdateStudentDto = {
  id: number;
  firstName?: string;
  lastName?: string;
  email?: string;
  gender?: keyof typeof GENDER_TYPES | null;
  classId?: number | null;
  houseId?: number | null;
  mentorGroupIds?: number[] | null;
  status?: keyof typeof STATUS;
  schoolId: number;
  updatedBy: number;
};
