import {
  routes,
  featureLevel,
  schoolPost,
  schoolPatch,
} from '../../utils/index.ts';
import { Container } from 'typedi';
import StudentService from './studentService.ts';
import { Right } from '../../auth/index.ts';
import { CurrentUser } from '../../models/genericTypes.ts';
import { createStudentSchema, updateStudentSchema } from './schemas/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default () => {
  schoolPost(
    featureLevel.production,
    Right.studentManagement.CREATE_STUDENT,
    routes.studentManagement.CREATE_STUDENT,
    async (req) => {
      const service = Container.get(StudentService);
      const dto = await createStudentSchema.validateAsync({ ...req.body });
      return await service.createStudent(dto, req.currentUser as CurrentUser);
    }
  );

  schoolPatch(
    featureLevel.production,
    Right.studentManagement.UPDATE_STUDENT_BY_ID,
    routes.studentManagement.UPDATE_STUDENT_BY_ID,
    async (req) => {
      const service = Container.get(StudentService);
      const { id } = req.params;
      const dto = await updateStudentSchema.validateAsync({ ...req.body });
      return await service.updateStudentById(
        { ...dto, id: parserId(id) },
        req.currentUser as CurrentUser
      );
    }
  );
};
