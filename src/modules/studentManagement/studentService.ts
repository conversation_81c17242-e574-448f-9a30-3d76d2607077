import { Container } from 'typedi';
import BaseService from '../../externalServices/baseService.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import { commonCatchHandler } from '../../utils/commonFunctions.ts';
import { CreateStudentDto, UpdateStudentDto } from './types.ts';
import StudentDao from './studentDao.ts';
import {
  formatErrorResponse,
  formatSuccessResponse,
  messageResponse,
} from '../../utils/apiResponses.ts';
import { HttpException } from '../../utils/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default class StudentService extends BaseService {
  private dao: StudentDao;

  constructor() {
    super();
    this.dao = Container.get(StudentDao);
  }

  // Services
  async createStudent(dto: CreateStudentDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'createStudent';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isCreated = await this.dao.createStudent(
          client,
          { ...dto, schoolId },
          actionUser
        );
        if (!isCreated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToCreate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'createdSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async updateStudentById(dto: UpdateStudentDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'updateStudent';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isUpdated = await this.dao.updateStudent(client, {
          ...dto,
          schoolId,
          updatedBy: actionUser.id,
        });
        if (!isUpdated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToUpdate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'updatedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }
}
