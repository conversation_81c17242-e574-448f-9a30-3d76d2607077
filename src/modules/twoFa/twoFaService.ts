import { Container } from 'typedi';
import moment from 'moment';
import {
  HttpException,
  // MESSAGE_TYPES,
  STATUS,
  ACTION_TYPE,
  formatErrorResponse,
  formatSuccessResponse,
  // getRandomNumber,
  messageResponse,
  randomNumber,
  TOKEN_TYPE,
  handleError,
  SUCCESS_ACTION_TYPE,
  commonCatchHandler,
  MESSAGE_TYPES,
} from '../../utils/index.ts';
import { BaseService, MessageService } from '../../externalServices/index.ts';
import type { PoolClient } from 'pg';
import type { CurrentUser, Id } from '../../models/genericTypes.ts';
import {
  // SendCode,
  TwoFaDto,
  SetTwoFaActionDto,
  ModifyTwoFaActionDto,
  VerifyTwoFaActionDto,
  VerifyTwoFaDto,
  ActionUser,
  SendCode,
} from './types.ts';
import { MappedUser } from '../userManagement/types.ts';
import TwoFaDao from './twoFaDao.ts';

export default class TwoFaService extends BaseService {
  private dao: TwoFaDao;

  constructor() {
    super();
    this.dao = Container.get(TwoFaDao);
  }

  async getTwoFaMethods() {
    return await this.txs.withTransaction(async (client: PoolClient) => {
      const result = await this.dao.getTwoFaMethods(client);
      return result;
    });
  }

  async getUserTwoFaMethods(actionUser: CurrentUser) {
    return await this.txs.withTransaction(async (client: PoolClient) => {
      const res = await this.getTwoFaMethodsByUserId(client, actionUser.id);
      if (!res) {
        return [];
      }

      return [res];
    });
  }

  async setTwoFa(dto: TwoFaDto, actionUser: ActionUser) {
    const actionDto = TwoFaService.setTwoFaDto(dto, actionUser);
    return await this.txs.withTransaction(
      async (client: PoolClient) =>
        await this.enableTwoFa(client, actionDto, actionUser)
    );
  }

  async enableTwoFa(
    client: PoolClient,
    actionDto: SetTwoFaActionDto,
    actionUser: ActionUser
  ) {
    const messageKey = 'twoFactorAuthentication';
    try {
      const result = await this.dao.getUserTwoFa(client, actionDto);
      if (result) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'alreadyExists')
        );
      }
      const isSuccess = await this.sendTwoFaToken(
        client,
        actionDto,
        actionUser
      );
      if (!isSuccess) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToSendOtp')
        );
      }
      return messageResponse(
        formatSuccessResponse(messageKey, 'otpSentSuccessFully')
      );
    } catch (error: any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  async sendTwoFaToken(
    client: PoolClient,
    actionDto: SetTwoFaActionDto,
    actionUser: ActionUser | MappedUser
  ) {
    const messageKey = 'sendTwoFaToken';
    try {
      const token = actionDto.isUnitTest ? 999999 : randomNumber(6);
      await this.dao.delete2FaToken(client, actionDto);
      const isTokenAdded = await this.dao.addTwoFaToken(client, actionDto, {
        token,
        validitySecs: 60,
      });
      if (!isTokenAdded) {
        return false;
      }

      const userCurrent2Fa = await this.dao.getTwoFaMethodById(
        client,
        actionDto.methodId
      );
      if (userCurrent2Fa.code === MESSAGE_TYPES.EMAIL) {
        await MessageService.sendTwoFaConfirmationCode(
          {
            ...actionDto,
            firstName: actionUser.firstName,
            email: actionDto.senderDetail,
          },
          {
            token,
            validitySecs: 60,
          }
        );
      }

      return true;
    } catch (error: any) {
      console.log(error);
      throw commonCatchHandler(error, messageKey);
    }
  }

  async confirmTwoFa(dto: VerifyTwoFaDto, actionUser: ActionUser) {
    const messageKey = 'twoFactorAuthentication';
    const actionDto = TwoFaService.verifyTwoFaDto(dto, actionUser);
    return await this.txs.withTransaction(async (client: PoolClient) => {
      try {
        return await this.verifyTwoFa(client, actionDto, actionUser);
      } catch (error: any) {
        console.log(error);
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToVerify')
        );
      }
    });
  }

  async verifyTwoFa(
    client: PoolClient,
    actionDto: VerifyTwoFaActionDto,
    actionUser: ActionUser
  ) {
    try {
      const updatedDto = { ...actionDto };
      const messageKey = 'twoFactorAuthentication';
      let isSetUp = false;
      let id = null;
      const result = await this.dao.getUserTwoFa(client, updatedDto);

      if (!result && updatedDto.actionType !== TOKEN_TYPE[ACTION_TYPE.ENABLE]) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'notATwoFaUser')
        );
      }

      if (result && updatedDto.actionType === TOKEN_TYPE[ACTION_TYPE.MODIFY]) {
        updatedDto.currentMethodId = result.two_fa_method_id;
      }
      const isVerified = await this.verifyTwoFaToken(client, updatedDto);
      if (!isVerified) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'inValidToken')
        );
      }
      // const recoveryCodes = await this.dao.getRecoveryCodes(client, updatedDto);
      if (
        // !recoveryCodes &&
        !result &&
        updatedDto.actionType === TOKEN_TYPE[ACTION_TYPE.ENABLE]
      ) {
        // if (!(await this.addRecoveryCodes(client, actionUser))) {
        //   throw new HttpException.NotFound(
        //     formatErrorResponse(messageKey, 'unableToAddRecoveryCodes')
        //   );
        // }

        const isAdded = await this.dao.setUpUserTwoFa(client, updatedDto);
        isSetUp = true;
        if (!isAdded) {
          throw new HttpException.NotFound(
            formatErrorResponse('twoFactorAuthentication', 'unableToAdd')
          );
        }
        id = isAdded.id;
      }

      if (
        await this.enableOrDisableTwoFa(client, actionDto, isSetUp, actionUser)
      ) {
        return messageResponse(
          formatSuccessResponse(
            'twoFactorAuthentication',
            'updatedSuccessfully'
          )
        );
      }

      if (await this.verifyTwoFaModification(client, updatedDto)) {
        return messageResponse(
          formatSuccessResponse(
            'twoFactorAuthentication',
            'updatedSuccessfully'
          )
        );
      }
      await this.updateTwoFaTokenHistory(client, {
        ...updatedDto,
      });

      // const res = await this.dao.getRecoveryCodes(client, {
      //   ...updatedDto,
      // });
      const methodDetails = await this.dao.getTwoFaMethodById(
        client,
        updatedDto.methodId
      );
      return {
        ...messageResponse(
          formatSuccessResponse(
            'twoFactorAuthentication',
            'verifiedSuccessfully'
          )
        ),
        data: {
          id,
          method: { name: methodDetails.name, id: methodDetails.id },
          // records: TwoFaService.formatRecoveryCodes(res),
          senderDetail: updatedDto.senderDetail,
          status: SUCCESS_ACTION_TYPE[updatedDto.actionType],
        },
      };
    } catch (error: any) {
      throw handleError(error, 'twoFactorAuthentication', 'failed');
    }
  }

  async enableOrDisableTwoFa(
    client: PoolClient,
    updatedDto: VerifyTwoFaActionDto,
    isSetUp: boolean,
    actionUser: ActionUser
  ) {
    const messageKey = 'twoFactorAuthentication';
    if (
      (updatedDto.actionType === TOKEN_TYPE[ACTION_TYPE.ENABLE] ||
        updatedDto.actionType === TOKEN_TYPE[ACTION_TYPE.DISABLE]) &&
      !isSetUp
    ) {
      const status =
        updatedDto.actionType === TOKEN_TYPE[ACTION_TYPE.ENABLE]
          ? STATUS.ACTIVE
          : STATUS.INACTIVE;
      const isUpdated = await this.dao.changeUser2FaStatus(client, {
        status,
        userId: actionUser.id,
        methodId: updatedDto.methodId,
      });

      if (!isUpdated)
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToUpdateUser2fa')
        );

      await this.updateTwoFaTokenHistory(client, updatedDto);
      return true;
    }
    return false;
  }

  async verifyTwoFaModification(
    client: PoolClient,
    updatedDto: VerifyTwoFaActionDto
  ) {
    const messageKey = 'twoFactorAuthentication';
    if (updatedDto.actionType === TOKEN_TYPE[ACTION_TYPE.MODIFY]) {
      const isNewMethodTokenVerified = await this.verifyTwoFaToken(
        client,
        {
          ...updatedDto,
          currentMethodId: null,
          token: updatedDto.newMethodVerificationToken,
        }
        // true
      );

      if (!isNewMethodTokenVerified) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'inValidNewMethodVerificationToken')
        );
      }

      const isUpdated = await this.dao.updateUser2FaSettings(
        client,
        updatedDto
      );
      if (!isUpdated) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToUpdateUser2fa')
        );
      }

      await this.updateTwoFaTokenHistory(client, {
        ...updatedDto,
        token: updatedDto.newMethodVerificationToken,
      });
      return true;
    }

    return false;
  }

  async updateTwoFaTokenHistory(
    client: PoolClient,
    actionDto: VerifyTwoFaActionDto
  ) {
    const isUpdated = await this.dao.updateTwoFaTokenHistory(client, actionDto);
    if (!isUpdated) {
      throw new HttpException.NotFound(
        formatErrorResponse('twoFactorAuthentication', 'failed')
      );
    }

    await this.dao.delete2FaToken(client, actionDto);
    return isUpdated;
  }

  // async verifyRecoveryCodes(
  //   client: PoolClient,
  //   actionDto: VerifyTwoFaActionDto
  // ) {
  //   const messageKey = 'twoFactorAuthentication';
  //   const recoveryCodes = await this.dao.getRecoveryCodes(client, {
  //     ...actionDto,
  //     isVerification: true,
  //     status: RECOVERY_CODE_STATUS.AVAILABLE,
  //   });

  //   if (!recoveryCodes) {
  //     throw new HttpException.NotFound(
  //       formatErrorResponse(messageKey, 'failed')
  //     );
  //   }
  //   return true;
  // }

  // async markRecoveryCodeConsumed(
  //   client: PoolClient,
  //   actionDto: VerifyTwoFaActionDto
  // ) {
  //   return this.dao.markRecoveryCodeConsumed(client, actionDto);
  // }

  // async addRecoveryCodes(client: PoolClient, actionUser: CurrentUser) {
  //   const tokens = getRandomNumber(5);
  //   const isAdded = await this.dao.addRecoveryCodes(client, {
  //     tokens,
  //     userId: actionUser.id,
  //   });
  //   return isAdded;
  // }

  async verifyTwoFaToken(
    client: PoolClient,
    actionDto: VerifyTwoFaActionDto
    // isSecondaryToken = false
  ) {
    const result = await this.dao.get2FaToken(client, actionDto);
    // if (!result) {
    //   const isSuccess = await this.isValidRecoverCode(client, actionDto);
    //   return isSuccess;
    // }
    if (!result) return false;

    const startMoment = moment.utc(result.created_on);
    const endMoment = moment
      .utc(result.created_on)
      .add(result.validity_seconds, 'seconds');
    const isInMoment = moment().utc().isBetween(startMoment, endMoment);
    // if (!isInMoment && !isSecondaryToken) {
    //   const isSuccess = await this.isValidRecoverCode(client, actionDto);
    //   return isSuccess;
    // }

    return isInMoment;
  }

  // async isValidRecoverCode(
  //   client: PoolClient,
  //   actionDto: VerifyTwoFaActionDto
  // ) {
  //   const isRecoveryCodeSuccess = await this.verifyRecoveryCodes(client, {
  //     ...actionDto,
  //     token: actionDto.token,
  //   });
  //   if (!isRecoveryCodeSuccess) return false;

  //   const isConsumed = await this.markRecoveryCodeConsumed(client, {
  //     ...actionDto,
  //   });
  //   return isConsumed;
  // }

  async updateTwoFa(dto: TwoFaDto, actionUser: ActionUser) {
    const messageKey = 'twoFactorAuthentication';
    return await this.txs.withTransaction(async (client: PoolClient) => {
      try {
        const actionDto = TwoFaService.modifyTwoFaDto(dto, actionUser);
        return await this.modifyTwoFa(client, actionDto, actionUser);
      } catch (error: any) {
        throw handleError(error, messageKey, 'unableToUpdate');
      }
    });
  }

  async modifyTwoFa(
    client: PoolClient,
    actionDto: ModifyTwoFaActionDto,
    actionUser: CurrentUser
  ) {
    const messageKey = 'twoFactorAuthentication';
    const result = await this.dao.getUserTwoFa(client, {
      ...actionDto,
      status:
        actionDto.actionType === TOKEN_TYPE[ACTION_TYPE.ENABLE]
          ? STATUS.INACTIVE
          : STATUS.ACTIVE,
    });

    TwoFaService.verifyTwoFaAction(actionDto, result);

    await this.dao.delete2FaToken(client, { ...actionDto, isUpdate: true });
    const currentMethodToken = actionDto.isUnitTest ? 999999 : randomNumber(6);
    const isCurrentTokenAdded = await this.dao.addTwoFaToken(
      client,
      {
        ...actionDto,
        methodId: result.two_fa_method_id,
        tokenType: actionDto.actionType,
      },
      {
        token: currentMethodToken,
        validitySecs: 60,
      }
    );
    if (!isCurrentTokenAdded) {
      throw new HttpException.NotFound(
        formatErrorResponse(messageKey, 'unAbleToUpdate')
      );
    }

    const res = await this.dao.getUserTwoFaMethods(client, {
      userId: actionUser.id,
    });
    // For verifying code
    await this.sendCode({
      code: res?.method.code,
      senderDetail: res?.senderDetail,
      token: currentMethodToken,
      firstName: actionUser.firstName,
    });

    if (actionDto.actionType === TOKEN_TYPE[ACTION_TYPE.MODIFY]) {
      throw new HttpException.BadRequest(
        formatErrorResponse(messageKey, 'notAllowed')
      );
    }

    // For changing method - Currently not used
    // if (actionDto.actionType === TOKEN_TYPE[ACTION_TYPE.MODIFY]) {
    //   const newMethodToken = actionDto.isUnitTest ? 999991 : randomNumber(6);
    //   const isNewMethodTokenAdded = await this.dao.addTwoFaToken(
    //     client,
    //     {
    //       ...actionDto,
    //       actionType: actionDto.actionType,
    //     },
    //     {
    //       token: newMethodToken,
    //       validitySecs: 60,
    //     }
    //   );
    //   if (!isNewMethodTokenAdded) {
    //     throw new HttpException.NotFound(
    //       formatErrorResponse(messageKey, 'unAbleToUpdate')
    //     );
    //   }
    //   const requestMethod = await this.dao.getTwoFaMethodById(
    //     client,
    //     actionDto.methodId
    //   );
    //   await this.sendCode({
    //     code: requestMethod.code,
    //     senderDetail: actionDto.senderDetail,
    //     token: newMethodToken,
    //     firstName: actionUser.firstName,
    //   });
    // }
    return {
      ...messageResponse(
        formatSuccessResponse('twoFactorAuthentication', 'otpSentSuccessfully')
      ),
      ...res,
    };
  }

  async sendCode(twoFaDetails: SendCode) {
    // if (twoFaDetails.code === MESSAGE_TYPES.SMS) {
    //   await SMSService.sendSms(`${'+'}${twoFaDetails.senderDetail}`, {
    //     code: twoFaDetails.token,
    //   });
    // }
    if (twoFaDetails.code === MESSAGE_TYPES.EMAIL) {
      await MessageService.sendTwoFaConfirmationCode(
        {
          ...twoFaDetails,
          firstName: twoFaDetails.firstName,
          email: twoFaDetails.senderDetail,
        },
        {
          token: twoFaDetails.token,
          validitySecs: 60,
        }
      );
    }
  }

  async getTwoFaMethodsByUserId(client: PoolClient, id: Id) {
    const result = await this.dao.getUserTwoFaMethods(client, {
      userId: id,
    });
    return result;
  }

  static verifyTwoFaAction(actionDto: ModifyTwoFaActionDto, result: any) {
    const messageKey = 'twoFactorAuthentication';
    if (!result) {
      throw new HttpException.NotFound(
        formatErrorResponse(messageKey, 'notATwoFaUser')
      );
    }

    if (actionDto.actionType === TOKEN_TYPE[ACTION_TYPE.MODIFY]) {
      if (actionDto.senderDetail === result.sender_details) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'unableToUpdateWithSameSenderDetails')
        );
      }
    }

    if (
      actionDto.actionType === TOKEN_TYPE[ACTION_TYPE.ENABLE] &&
      result.status === STATUS.ACTIVE
    ) {
      throw new HttpException.NotFound(
        formatErrorResponse(messageKey, 'alreadyEnabled')
      );
    }

    if (
      actionDto.actionType === TOKEN_TYPE[ACTION_TYPE.DISABLE] &&
      result.status === STATUS.INACTIVE
    ) {
      throw new HttpException.NotFound(
        formatErrorResponse(messageKey, 'alreadyDisabled')
      );
    }
  }

  static formatRecoveryCodes(rows: any[]) {
    const result: any = [];
    rows.forEach((row) => {
      const obj = {
        id: row.id,
        code: row.recovery_code,
      };
      result.push(obj);
    });

    return result;
  }

  static setTwoFaDto(dto: TwoFaDto, actionUser: ActionUser) {
    return {
      userId: actionUser.id,
      ip: actionUser.ip,
      userAgent: actionUser.userAgent ?? '',
      methodId: dto.methodId,
      senderDetail: dto.senderDetail,
      isUnitTest: actionUser.isUnitTest,
      tokenType: TOKEN_TYPE[ACTION_TYPE.ENABLE],
    };
  }

  static verifyTwoFaDto(dto: VerifyTwoFaDto, actionUser: ActionUser) {
    return {
      userId: actionUser.id,
      methodId: dto.methodId,
      senderDetail: dto.senderDetail,
      token: dto.verificationToken,
      newMethodVerificationToken: dto?.newMethodVerificationToken,
      actionType: TOKEN_TYPE[dto.actionType],
      ip: actionUser.ip,
      userAgent: actionUser.userAgent,
    };
  }

  static modifyTwoFaDto(dto: TwoFaDto, actionUser: ActionUser) {
    return {
      userId: actionUser.id,
      ip: actionUser.ip,
      userAgent: actionUser.userAgent,
      methodId: dto.methodId,
      senderDetail: dto.senderDetail,
      isUnitTest: actionUser.isUnitTest,
      actionType: TOKEN_TYPE[dto.actionType],
    };
  }
}
