import {
  routes,
  featureLevel,
  schoolPost,
  schoolGet,
  schoolPatch,
  schoolDelete,
} from '../../utils/index.ts';
import { Container } from 'typedi';
import MentoringGroupService from './mentoringGroupService.ts';
import { Right } from '../../auth/index.ts';
import { CurrentUser } from '../../models/genericTypes.ts';
import {
  createMentorGroupSchema,
  updateMentorGroupSchema,
  mentorIdsSchema,
} from './schemas/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { studentIdsSchema } from '../../models/scheams/index.ts';

export default () => {
  schoolPost(
    featureLevel.production,
    Right.mentoringGroupManagement.CREATE_MENTORING_GROUP,
    routes.mentoringGroupManagement.CREATE_MENTORING_GROUP,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const dto = await createMentorGroupSchema.validateAsync({ ...req.body });
      return await service.createMentoringGroup(
        dto,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolGet(
    featureLevel.production,
    Right.mentoringGroupManagement.GET_MENTORING_GROUP_BY_ID,
    routes.mentoringGroupManagement.GET_MENTORING_GROUP_BY_ID,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      return await service.getMentoringGroupById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPatch(
    featureLevel.production,
    Right.mentoringGroupManagement.UPDATE_MENTORING_GROUP_BY_ID,
    routes.mentoringGroupManagement.UPDATE_MENTORING_GROUP_BY_ID,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      const dto = await updateMentorGroupSchema.validateAsync({ ...req.body });
      return await service.updateMentoringGroupById(
        { ...dto, id },
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.mentoringGroupManagement.DELETE_MENTORING_GROUP_BY_ID,
    routes.mentoringGroupManagement.DELETE_MENTORING_GROUP_BY_ID,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      return await service.deleteMentoringGroupById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  // schoolGet(
  //   featureLevel.production,
  //   Right.mentoringGroupManagement.GET_MENTORING_GROUPS_LIST,
  //   routes.mentoringGroupManagement.GET_MENTORING_GROUPS_LIST,
  //   async (req) => {
  //     const service = Container.get(MentoringGroupService);
  //     return await service.getMentoringGroupList(req.currentUser as CurrentUser);
  //   }
  // );

  schoolPost(
    featureLevel.production,
    Right.mentoringGroupManagement.ADD_STUDENTS_TO_MENTORING_GROUP_IN_BULK,
    routes.mentoringGroupManagement.ADD_STUDENTS_TO_MENTORING_GROUP_IN_BULK,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      const dto = await studentIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.addStudentsToMentoringGroupInBulk(
        parserId(id),
        dto.studentIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.mentoringGroupManagement.REMOVE_STUDENTS_FROM_MENTORING_GROUP_IN_BULK,
    routes.mentoringGroupManagement
      .REMOVE_STUDENTS_FROM_MENTORING_GROUP_IN_BULK,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      const dto = await studentIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.removeStudentsFromMentoringGroupInBulk(
        parserId(id),
        dto.studentIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.mentoringGroupManagement.REMOVE_STUDENT_FROM_MENTORING_GROUP,
    routes.mentoringGroupManagement.REMOVE_STUDENT_FROM_MENTORING_GROUP,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id, studentId } = req.params;
      return await service.removeStudentMentoringGroup(
        parserId(id),
        parserId(studentId),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPost(
    featureLevel.production,
    Right.mentoringGroupManagement.ADD_STAFFS_TO_MENTORING_GROUP_IN_BULK,
    routes.mentoringGroupManagement.ADD_STAFFS_TO_MENTORING_GROUP_IN_BULK,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      const dto = await mentorIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.addStaffsToMentoringGroupInBulk(
        parserId(id),
        dto.staffIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.mentoringGroupManagement.REMOVE_STAFFS_FROM_MENTORING_GROUP_IN_BULK,
    routes.mentoringGroupManagement.REMOVE_STAFFS_FROM_MENTORING_GROUP_IN_BULK,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id } = req.params;
      const dto = await mentorIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.removeStaffsFromMentoringGroupInBulk(
        parserId(id),
        dto.staffIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.mentoringGroupManagement.REMOVE_STAFF_FROM_MENTORING_GROUP,
    routes.mentoringGroupManagement.REMOVE_STAFF_FROM_MENTORING_GROUP,
    async (req) => {
      const service = Container.get(MentoringGroupService);
      const { id, staffId } = req.params;
      return await service.removeStaffFormMentoringGroup(
        parserId(id),
        parserId(staffId),
        req.currentUser as CurrentUser
      );
    }
  );
};
