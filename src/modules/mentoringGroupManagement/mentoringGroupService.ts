import { Container } from 'typedi';
import BaseService from '../../externalServices/baseService.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import { commonCatchHandler } from '../../utils/commonFunctions.ts';
import { CreateMentorGroupDto, UpdateMentorGroupDto } from './types.ts';
import MentoringGroupDao from './mentoringGroupDao.ts';
import {
  formatErrorResponse,
  formatSuccessResponse,
  messageResponse,
} from '../../utils/apiResponses.ts';
import { PoolClient } from 'pg';
import { HttpException, STATUS } from '../../utils/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default class MentoringGroupService extends BaseService {
  private dao: MentoringGroupDao;

  constructor() {
    super();
    this.dao = Container.get(MentoringGroupDao);
  }

  async findSchoolMentoringGroup(
    client: PoolClient,
    schoolId: Id,
    mentorGroupId: Id
  ) {
    const messageKey = 'schoolMentoringGroup';
    try {
      const schoolMentorGroup = await this.dao.findSchoolMentoringGroup(
        client,
        schoolId,
        mentorGroupId
      );
      if (!schoolMentorGroup) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'notFound')
        );
      }
      if (schoolMentorGroup.status === STATUS.INACTIVE) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'inactive')
        );
      }
      return schoolMentorGroup;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  // Services
  async createMentoringGroup(
    dto: CreateMentorGroupDto,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'createMentoringGroup';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isCreated = await this.dao.createMentoringGroup(
          client,
          { ...dto, schoolId },
          actionUser
        );
        if (!isCreated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToCreate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'createdSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async getMentoringGroupById(mentorGroupId: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'mentoringGroup';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const mentorGroupDetails = await this.dao.getMentoringGroupById(
          client,
          mentorGroupId,
          schoolId
        );
        if (!mentorGroupDetails) {
          throw new HttpException.NotFound(
            formatErrorResponse(messageKey, 'notFound')
          );
        }
        return mentorGroupDetails;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async updateMentoringGroupById(
    dto: UpdateMentorGroupDto,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'updateMentoringGroup';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isUpdated = await this.dao.updateMentoringGroup(client, {
          ...dto,
          schoolId,
          updatedBy: actionUser.id,
        });
        if (!isUpdated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToUpdate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'updatedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async deleteMentoringGroupById(id: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'deleteMentoringGroup';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isDeleted = await this.dao.deleteMentoringGroupById(
          client,
          id,
          schoolId
        );
        if (!isDeleted) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToDelete')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'deletedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async addStudentsToMentoringGroupInBulk(
    mentorGroupId: Id,
    studentIds: Id[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'addStudentsToMentoringGroupInBulk';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isSuccess = await this.dao.updateMentoringGroupForStudents(
          client,
          schoolId,
          mentorGroupId,
          studentIds,
          'add'
        );
        if (!isSuccess) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToAdd')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'addedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStudentsFromMentoringGroupInBulk(
    mentorGroupId: Id,
    studentIds: Id[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStudentsFromMentoringGroupInBulk';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isSuccess = await this.dao.updateMentoringGroupForStudents(
          client,
          schoolId,
          mentorGroupId,
          studentIds,
          'remove'
        );
        if (!isSuccess) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStudentMentoringGroup(
    mentorGroupId: Id,
    studentId: Id,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStudentMentoringGroup';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateMentoringGroupForStudents(
          client,
          schoolId,
          mentorGroupId,
          [studentId],
          'remove'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async addStaffsToMentoringGroupInBulk(
    mentorGroupId: Id,
    staffIds: Id[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'addStaffsToMentoringGroupInBulk';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateMentoringGroupForStaffs(
          client,
          schoolId,
          mentorGroupId,
          staffIds,
          'add'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToAdd')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'addedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStaffsFromMentoringGroupInBulk(
    mentorGroupId: Id,
    staffIds: Id[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStaffsFromMentoringGroupInBulk';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateMentoringGroupForStaffs(
          client,
          schoolId,
          mentorGroupId,
          staffIds,
          'remove'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStaffFormMentoringGroup(
    mentorGroupId: Id,
    staffId: Id,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStaffFormClass';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateMentoringGroupForStaffs(
          client,
          schoolId,
          mentorGroupId,
          [staffId],
          'remove'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }
}
