import { PoolClient } from 'pg';
import { CreateMentorGroupDto, UpdateMentorGroupDto } from './types.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import Mapper from '../../utils/daoHelper/mapper.ts';
import Queries from '../../utils/daoHelper/queries.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import mentorGroupUpdateMap from './updateMaps/mentorGroupUpdateMap.ts';

export default class MentoringGroupDao {
  async createMentoringGroup(
    client: PoolClient,
    dto: CreateMentorGroupDto,
    actionUser: CurrentUser
  ) {
    const res = await client.query(
      `INSERT INTO mentoring_groups 
      (name, description, school_id, status, created_by, updated_by) 
      VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [
        dto.name,
        dto.description,
        dto.schoolId,
        dto.status,
        actionUser.id,
        actionUser.id,
      ]
    );
    const mentorGroupId = Mapper.getId(res);

    const query = `
      INSERT INTO mentoring_group_users (user_id, mentoring_group_id)
      VALUES ($1, $2);
    `;

    await Promise.all(
      dto.mentorIds.map((userId) =>
        client.query(query, [userId, mentorGroupId])
      )
    );
    return mentorGroupId;
  }

  async getMentoringGroupById(
    client: PoolClient,
    mentorGroupId: Id,
    schoolId: Id
  ) {
    const query = `
      SELECT
        mg.id,
        mg.name,
        mg.school_id,
        mg.description,
        mg.status,
        COUNT(DISTINCT smg.id) AS student_count,
        COUNT(DISTINCT mgu.user_id) AS mentor_count
      FROM mentoring_groups mg
      LEFT JOIN student_mentoring_groups smg ON smg.mentoring_group_id = mg.id
      LEFT JOIN class_users cu ON mgu.mentoring_group_id = mg.id
      WHERE mg.id = $1 AND mg.school_id = $2 AND mg.status = 'ACTIVE'
      GROUP BY mg.id, mg.name, mg.school_id, mg.description, mg.status;
    `;
    const res = await client.query(query, [mentorGroupId, schoolId]);
    return MentoringGroupDao.mapMentoringGroupDetails(res.rows);
  }

  async updateMentoringGroup(client: PoolClient, dto: UpdateMentorGroupDto) {
    const { sql, args } = Queries.updaterFor(
      'mentoring_groups',
      mentorGroupUpdateMap,
      dto
    );
    const res = await client.query(sql, args);
    return res.rowCount === 1;
  }

  async deleteMentoringGroupById(client: PoolClient, id: Id, schoolId: Id) {
    const res1 = await client.query(
      'DELETE FROM mentoring_group_users WHERE mentoring_group_id = $1',
      [id]
    );
    const res2 = await client.query(
      'DELETE FROM student_mentoring_groups WHERE mentoring_group_id = $1',
      [id]
    );
    const res3 = await client.query(
      'DELETE FROM mentoring_groups WHERE id = $1 AND school_id = $2',
      [id, schoolId]
    );
    return res1.rowCount === 1 && res2.rowCount === 1 && res3.rowCount === 1;
  }

  async findSchoolMentoringGroup(
    client: PoolClient,
    schoolId: number,
    mentorGroupId: number
  ) {
    const query =
      'SELECT * FROM mentoring_groups WHERE school_id = $1 AND id = $2';
    const result = await client.query(query, [schoolId, mentorGroupId]);
    return MentoringGroupDao.mapSchoolMentoringGroup(result.rows);
  }

  async updateMentoringGroupForStudents(
    client: PoolClient,
    schoolId: number,
    mentorGroupId: number,
    studentIds: Id[],
    type: 'add' | 'remove'
  ) {
    if (!studentIds.length) return false;

    let result = false;

    if (type === 'add') {
      const res = await client.query(
        `
      INSERT INTO student_mentoring_groups (student_id, mentoring_group_id)
      SELECT s.id, mg.id
      FROM students s
      JOIN mentoring_groups mg
        ON mg.id = $1 
       AND mg.school_id = $2 
       AND mg.status = 'ACTIVE'
      WHERE s.id = ANY($3)
        AND s.school_id = $2
        AND s.status = 'ACTIVE'
      ON CONFLICT (student_id, mentoring_group_id) DO NOTHING
      `,
        [mentorGroupId, schoolId, studentIds]
      );

      result = (res.rowCount ?? 0) > 0;
    } else {
      const res = await client.query(
        `
      DELETE FROM student_mentoring_groups smg
      USING students s
      WHERE smg.student_id = s.id
        AND smg.mentoring_group_id = $1
        AND s.id = ANY($2)
        AND s.school_id = $3
        AND s.status = 'ACTIVE'
      `,
        [mentorGroupId, studentIds, schoolId]
      );

      result = (res.rowCount ?? 0) > 0;
    }

    return result;
  }

  async updateMentoringGroupForStaffs(
    client: PoolClient,
    schoolId: number,
    mentorGroupId: number,
    staffIds: number[],
    type: 'add' | 'remove'
  ): Promise<boolean> {
    let result = false;

    if (type === 'add') {
      const res = await client.query(
        `
        INSERT INTO mentoring_group_users (mentoring_group_id, user_id)
        SELECT $1, su.user_id
        FROM school_users su
        WHERE su.school_id = $2 AND su.user_id = ANY($3)
        ON CONFLICT (mentoring_group_id, user_id) DO NOTHING
      `,
        [mentorGroupId, schoolId, staffIds]
      );
      result = (res.rowCount ?? 0) > 0;
    }

    if (type === 'remove') {
      const res = await client.query(
        `
        DELETE FROM mentoring_group_users mgu
        WHERE mgu.mentoring_group_id = $1
          AND mgu.user_id = ANY($2)
          AND EXISTS (
            SELECT 1 FROM school_users su
            WHERE su.user_id = mgu.user_id
              AND su.school_id = $3
          )
      `,
        [mentorGroupId, staffIds, schoolId]
      );
      result = (res.rowCount ?? 0) > 0;
    }

    return result;
  }

  static mapSchoolMentoringGroup(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      name: firstRow.name,
      description: firstRow.description,
      status: firstRow.status,
    };
  }

  static mapMentoringGroupDetails(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      schoolId: parserId(firstRow.school_id),
      name: firstRow.name,
      description: firstRow.description,
      status: firstRow.status,
      studentCount: parserId(firstRow.student_count),
      mentorCount: parserId(firstRow.mentor_count),
    };
  }
}
