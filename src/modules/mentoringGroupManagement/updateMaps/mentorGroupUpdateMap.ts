import { filterUndefinedFromObject } from '../../../utils/index.ts';
import { UpdateMentorGroupDto } from '../types.ts';

export default (classObj: UpdateMentorGroupDto) => {
  const map = {
    id: classObj.id,
    name: classObj.name,
    description: classObj.description ?? undefined,
    status: classObj.status,
    updated_by: classObj.updatedBy,
  };

  return filterUndefinedFromObject(map);
};
