import Joi from 'joi';
import {
  requiredStringValidator,
  requiredEnumValidator,
  getEnumArrayFromObj,
  STATUS,
  requiredNumberArrayValidator,
  nullableStringValidator,
} from '../../../utils/index.ts';

export default Joi.object(
  ((messageKey) => ({
    name: requiredStringValidator(messageKey, 'name'),
    description: nullableStringValidator(messageKey, 'description'),
    mentorIds: requiredNumberArrayValidator(messageKey, 'mentorIds'),
    status: requiredEnumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('createMentorGroup')
).options({ stripUnknown: true });
