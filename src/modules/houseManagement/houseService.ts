import { Container } from 'typedi';
import BaseService from '../../externalServices/baseService.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import { commonCatchHandler } from '../../utils/commonFunctions.ts';
import { CreateHouseDto, UpdateHouseDto } from './types.ts';
import HouseDao from './houseDao.ts';
import {
  formatErrorResponse,
  formatSuccessResponse,
  messageResponse,
} from '../../utils/apiResponses.ts';
import { PoolClient } from 'pg';
import { HttpException, STATUS } from '../../utils/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default class HouseService extends BaseService {
  private dao: HouseDao;

  constructor() {
    super();
    this.dao = Container.get(HouseDao);
  }

  async findSchoolHouse(client: PoolClient, schoolId: number, houseId: number) {
    const messageKey = 'schoolHouse';
    try {
      const schoolHouse = await this.dao.findSchoolHouse(
        client,
        schoolId,
        houseId
      );
      if (!schoolHouse) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'notFound')
        );
      }
      if (schoolHouse.status === STATUS.INACTIVE) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'inactive')
        );
      }
      return schoolHouse;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  // Services
  async createHouse(dto: CreateHouseDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'createHouse';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isCreated = await this.dao.createHouse(
          client,
          { ...dto, schoolId },
          actionUser
        );
        if (!isCreated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToCreate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'createdSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async getHouseById(houseId: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'getHouse';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const houseDetails = await this.dao.getHouseById(
          client,
          houseId,
          schoolId
        );
        if (!houseDetails) {
          throw new HttpException.NotFound(
            formatErrorResponse(messageKey, 'notFound')
          );
        }
        return houseDetails;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async updateHouseById(dto: UpdateHouseDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'updateHouse';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isUpdated = await this.dao.updateHouse(client, {
          ...dto,
          schoolId,
          updatedBy: actionUser.id,
        });
        if (!isUpdated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToUpdate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'updatedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async deleteHouseById(id: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'deleteHouse';
      try {
        const schoolId = parserId(actionUser.schoolId);
        // First remove students from the class
        const isDeleted = await this.dao.deleteHouseById(client, id, schoolId);
        if (!isDeleted) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToDelete')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'deletedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async addStudentsToHouseInBulk(
    houseId: number,
    studentIds: number[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'addStudentsToHouseInBulk';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const successResults = await Promise.all(
          studentIds.map((studentId) =>
            this.dao.updateHouseForStudent(
              client,
              schoolId,
              houseId,
              studentId,
              actionUser,
              'add'
            )
          )
        );
        const allSucceeded = successResults.every((result) => result === true);
        if (!allSucceeded) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToAdd')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'addedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStudentsFromHouseInBulk(
    houseId: number,
    studentIds: number[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStudentsFromHouseInBulk';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const successResults = await Promise.all(
          studentIds.map((studentId) =>
            this.dao.updateHouseForStudent(
              client,
              schoolId,
              houseId,
              studentId,
              actionUser,
              'remove'
            )
          )
        );
        const allSucceeded = successResults.every((result) => result === true);
        if (!allSucceeded) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStudentFormHouse(
    houseId: Id,
    studentId: Id,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStudentFormHouse';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateHouseForStudent(
          client,
          schoolId,
          houseId,
          studentId,
          actionUser,
          'remove'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return success;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }
}
