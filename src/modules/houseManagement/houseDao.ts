import { PoolClient } from 'pg';
import { CreateHouseDto, UpdateHouseDto } from './types.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import Mapper from '../../utils/daoHelper/mapper.ts';
import Queries from '../../utils/daoHelper/queries.ts';
import houseUpdateMap from './updateMaps/houseUpdateMap.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';

export default class HouseDao {
  async createHouse(
    client: PoolClient,
    dto: CreateHouseDto,
    actionUser: CurrentUser
  ) {
    const res = await client.query(
      `INSERT INTO houses 
      (name, description, school_id, status, created_by, updated_by) 
      VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [
        dto.name,
        dto.description,
        dto.schoolId,
        dto.status,
        actionUser.id,
        actionUser.id,
      ]
    );
    return Mapper.getId(res);
  }

  async getHouseById(client: PoolClient, houseId: Id, schoolId: Id) {
    const query = `
      SELECT
        h.id,
        h.name,
        h.school_id,
        h.description,
        h.status,
        COUNT(DISTINCT s.id) AS student_count
      FROM houses h
      LEFT JOIN students s ON s.house_id = h.id
      WHERE h.id = $1
        AND h.school_id = $2
        AND h.status = 'ACTIVE'
      GROUP BY h.id, h.name, h.school_id, h.description, h.status;
    `;
    const res = await client.query(query, [houseId, schoolId]);
    return HouseDao.mapHouseDetails(res.rows);
  }

  async updateHouse(client: PoolClient, dto: UpdateHouseDto) {
    const { sql, args } = Queries.updaterFor('houses', houseUpdateMap, dto);
    const res = await client.query(sql, args);
    return res.rowCount === 1;
  }

  async deleteHouseById(client: PoolClient, id: Id, schoolId: Id) {
    const res = await client.query(
      'DELETE FROM houses WHERE house_id = $1 AND school_id = $2',
      [id, schoolId]
    );
    return res.rowCount === 1;
  }

  async findSchoolHouse(client: PoolClient, schoolId: number, houseId: number) {
    const query = 'SELECT * FROM houses WHERE school_id = $1 AND id = $2';
    const result = await client.query(query, [schoolId, houseId]);
    return HouseDao.mapSchoolHouse(result.rows);
  }

  async updateHouseForStudent(
    client: PoolClient,
    schoolId: number,
    houseId: number,
    studentId: number,
    actionUser: CurrentUser,
    type: 'add' | 'remove'
  ) {
    let result = false;
    if (type === 'add') {
      const res = await client.query(
        `UPDATE students
        SET house_id = $1, updated_by = $2
        WHERE id = $3
          AND school_id = $4
          AND status = 'ACTIVE'`,
        [houseId, actionUser.id, studentId, schoolId]
      );
      result = res.rowCount === 1;
    } else {
      const res = await client.query(
        `UPDATE students
        SET house_id = null, updated_by = $1
        WHERE id = $2
            AND school_id = $3
            AND status = 'ACTIVE'`,
        [actionUser.id, studentId, schoolId]
      );
      result = res.rowCount === 1;
    }
    return result;
  }

  static mapSchoolHouse(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      name: firstRow.name,
      description: firstRow.description,
      status: firstRow.status,
    };
  }

  static mapHouseDetails(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      schoolId: parserId(firstRow.school_id),
      name: firstRow.name,
      description: firstRow.description,
      status: firstRow.status,
      studentCount: parserId(firstRow.student_count),
    };
  }
}
