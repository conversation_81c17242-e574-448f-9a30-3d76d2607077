import Joi from 'joi';
import {
  requiredStringValidator,
  requiredEnumValidator,
  getEnumArrayFromObj,
  STATUS,
  nullableStringValidator,
} from '../../../utils/index.ts';

export default Joi.object(
  ((messageKey) => ({
    name: requiredStringValidator(messageKey, 'name'),
    description: nullableStringValidator(messageKey, 'description'),
    status: requiredEnumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('createHouse')
).options({ stripUnknown: true });
