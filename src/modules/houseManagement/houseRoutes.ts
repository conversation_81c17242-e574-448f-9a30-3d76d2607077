import {
  routes,
  featureLevel,
  schoolPost,
  schoolGet,
  schoolPatch,
  schoolDelete,
} from '../../utils/index.ts';
import { Container } from 'typedi';
import HouseService from './houseService.ts';
import { Right } from '../../auth/index.ts';
import { CurrentUser } from '../../models/genericTypes.ts';
import { createHouseSchema, updateHouseSchema } from './schemas/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { studentIdsSchema } from '../../models/scheams/index.ts';

export default () => {
  schoolPost(
    featureLevel.production,
    Right.houseManagement.CREATE_HOUSE,
    routes.houseManagement.CREATE_HOUSE,
    async (req) => {
      const service = Container.get(HouseService);
      const dto = await createHouseSchema.validateAsync({ ...req.body });
      return await service.createHouse(dto, req.currentUser as CurrentUser);
    }
  );

  schoolGet(
    featureLevel.production,
    Right.houseManagement.GET_HOUSE_BY_ID,
    routes.houseManagement.GET_HOUSE_BY_ID,
    async (req) => {
      const service = Container.get(HouseService);
      const { id } = req.params;
      return await service.getHouseById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPatch(
    featureLevel.production,
    Right.houseManagement.UPDATE_HOUSE_BY_ID,
    routes.houseManagement.UPDATE_HOUSE_BY_ID,
    async (req) => {
      const service = Container.get(HouseService);
      const { id } = req.params;
      const dto = await updateHouseSchema.validateAsync({ ...req.body });
      return await service.updateHouseById(
        { ...dto, id: parserId(id) },
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.houseManagement.DELETE_HOUSE_BY_ID,
    routes.houseManagement.DELETE_HOUSE_BY_ID,
    async (req) => {
      const service = Container.get(HouseService);
      const { id } = req.params;
      return await service.deleteHouseById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  // schoolGet(
  //   featureLevel.production,
  //   Right.houseManagement.GET_HOUSES_LIST,
  //   routes.houseManagement.GET_HOUSES_LIST,
  //   async (req) => {
  //     const service = Container.get(HouseService);
  //     return await service.getHousesList(req.currentUser as CurrentUser);
  //   }
  // );

  schoolPost(
    featureLevel.production,
    Right.houseManagement.ADD_STUDENTS_TO_HOUSE_IN_BULK,
    routes.houseManagement.ADD_STUDENTS_TO_HOUSE_IN_BULK,
    async (req) => {
      const service = Container.get(HouseService);
      const { id } = req.params;
      const dto = await studentIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.addStudentsToHouseInBulk(
        parserId(id),
        dto.studentIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.houseManagement.REMOVE_STUDENTS_FROM_HOUSE_IN_BULK,
    routes.houseManagement.REMOVE_STUDENTS_FROM_HOUSE_IN_BULK,
    async (req) => {
      const service = Container.get(HouseService);
      const { id } = req.params;
      const dto = await studentIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.removeStudentsFromHouseInBulk(
        parserId(id),
        dto.studentIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.houseManagement.REMOVE_STUDENT_FROM_HOUSE,
    routes.houseManagement.REMOVE_STUDENT_FROM_HOUSE,
    async (req) => {
      const service = Container.get(HouseService);
      const { id, studentId } = req.params;
      return await service.removeStudentFormHouse(
        parserId(id),
        parserId(studentId, 'studentId'),
        req.currentUser as CurrentUser
      );
    }
  );
};
