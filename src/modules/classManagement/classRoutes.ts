import {
  routes,
  featureLevel,
  schoolPost,
  schoolGet,
  schoolPatch,
  schoolDelete,
} from '../../utils/index.ts';
import { Container } from 'typedi';
import ClassService from './classService.ts';
import { Right } from '../../auth/index.ts';
import createClassSchema from './schemas/createClassSchema.ts';
import { CurrentUser } from '../../models/genericTypes.ts';
import { updateClassSchema, staffIdsSchema } from './schemas/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { studentIdsSchema } from '../../models/scheams/index.ts';
import Filter from '../../models/filter.ts';

export default () => {
  schoolPost(
    featureLevel.production,
    Right.classManagement.CREATE_CLASS,
    routes.classManagement.CREATE_CLASS,
    async (req) => {
      const service = Container.get(ClassService);
      const dto = await createClassSchema.validateAsync({ ...req.body });
      return await service.createClass(dto, req.currentUser as CurrentUser);
    }
  );

  schoolGet(
    featureLevel.production,
    Right.classManagement.GET_CLASS_BY_ID,
    routes.classManagement.GET_CLASS_BY_ID,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      return await service.getClassById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPatch(
    featureLevel.production,
    Right.classManagement.UPDATE_CLASS_BY_ID,
    routes.classManagement.UPDATE_CLASS_BY_ID,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      const dto = await updateClassSchema.validateAsync({ ...req.body });
      return await service.updateClassById(
        { ...dto, id },
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.classManagement.DELETE_CLASS_BY_ID,
    routes.classManagement.DELETE_CLASS_BY_ID,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      return await service.deleteClassById(
        parserId(id),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolGet(
    featureLevel.production,
    Right.classManagement.GET_CLASSES_LIST,
    routes.classManagement.GET_CLASSES_LIST,
    async (req) => {
      const filter = Filter.fromRequest(req, Filter.types.CLASSES_LIST);
      const service = Container.get(ClassService);
      return await service.getClassesList(
        filter,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPost(
    featureLevel.production,
    Right.classManagement.ADD_STUDENTS_TO_CLASS_IN_BULK,
    routes.classManagement.ADD_STUDENTS_TO_CLASS_IN_BULK,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      const dto = await studentIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.addStudentsToClassInBulk(
        parserId(id),
        dto.studentIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.classManagement.REMOVE_STUDENTS_FROM_CLASS_IN_BULK,
    routes.classManagement.REMOVE_STUDENTS_FROM_CLASS_IN_BULK,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      const dto = await studentIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.removeStudentsFromClassInBulk(
        parserId(id),
        dto.studentIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.classManagement.REMOVE_STUDENT_FROM_CLASS,
    routes.classManagement.REMOVE_STUDENT_FROM_CLASS,
    async (req) => {
      const service = Container.get(ClassService);
      const { id, studentId } = req.params;
      return await service.removeStudentFormClass(
        parserId(id),
        parserId(studentId),
        req.currentUser as CurrentUser
      );
    }
  );

  schoolPost(
    featureLevel.production,
    Right.classManagement.ADD_STAFFS_TO_CLASS_IN_BULK,
    routes.classManagement.ADD_STAFFS_TO_CLASS_IN_BULK,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      const dto = await staffIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.addStaffsToClassInBulk(
        parserId(id),
        dto.staffIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.classManagement.REMOVE_STAFFS_FROM_CLASS_IN_BULK,
    routes.classManagement.REMOVE_STAFFS_FROM_CLASS_IN_BULK,
    async (req) => {
      const service = Container.get(ClassService);
      const { id } = req.params;
      const dto = await staffIdsSchema.validateAsync({
        ...req.body,
      });
      return await service.removeStaffsFromClassInBulk(
        parserId(id),
        dto.staffIds,
        req.currentUser as CurrentUser
      );
    }
  );

  schoolDelete(
    featureLevel.production,
    Right.classManagement.REMOVE_STAFF_FROM_CLASS,
    routes.classManagement.REMOVE_STAFF_FROM_CLASS,
    async (req) => {
      const service = Container.get(ClassService);
      const { id, staffId } = req.params;
      return await service.removeStaffFormClass(
        parserId(id),
        parserId(staffId),
        req.currentUser as CurrentUser
      );
    }
  );
};
