import { Container } from 'typedi';
import BaseService from '../../externalServices/baseService.ts';
import { CurrentUser, Id } from '../../models/genericTypes.ts';
import { commonCatchHandler } from '../../utils/commonFunctions.ts';
import { CreateClassDto, UpdateClassDto } from './types.ts';
import ClassDao from './classDao.ts';
import {
  formatErrorResponse,
  formatSuccessResponse,
  messageResponse,
} from '../../utils/apiResponses.ts';
import { PoolClient } from 'pg';
import { HttpException, STATUS, validateParamsId } from '../../utils/index.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { ParsedFilter } from '../../models/filter.ts';

export default class ClassService extends BaseService {
  private dao: ClassDao;

  constructor() {
    super();
    this.dao = Container.get(ClassDao);
  }

  async findSchoolClass(client: PoolClient, schoolId: number, classId: number) {
    const messageKey = 'schoolClass';
    try {
      const schoolClass = await this.dao.findSchoolClass(
        client,
        schoolId,
        classId
      );
      if (!schoolClass) {
        throw new HttpException.NotFound(
          formatErrorResponse(messageKey, 'notFound')
        );
      }
      if (schoolClass.status === STATUS.INACTIVE) {
        throw new HttpException.BadRequest(
          formatErrorResponse(messageKey, 'inactive')
        );
      }
      return schoolClass;
    } catch (error: Error | any) {
      throw commonCatchHandler(error, messageKey);
    }
  }

  // Services
  async createClass(dto: CreateClassDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'createClass';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const isCreated = await this.dao.createClass(
          client,
          { ...dto, schoolId },
          actionUser
        );
        if (!isCreated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToCreate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'createdSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async getClassesList(filter: ParsedFilter, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'classesList';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const res = await this.dao.getClassesList(client, filter, schoolId);
        return res;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async getClassById(classId: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'class';
      try {
        validateParamsId(classId);
        const schoolId = parserId(actionUser.schoolId);
        const classDetails = await this.dao.getClassById(
          client,
          classId,
          schoolId
        );
        if (!classDetails) {
          throw new HttpException.NotFound(
            formatErrorResponse(messageKey, 'notFound')
          );
        }
        return classDetails;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async updateClassById(dto: UpdateClassDto, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'updateClass';
      try {
        validateParamsId(dto.id);
        const schoolId = parserId(actionUser.schoolId);
        const isUpdated = await this.dao.updateClass(client, {
          ...dto,
          schoolId,
          updatedBy: actionUser.id,
        });
        if (!isUpdated) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToUpdate')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'updatedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async deleteClassById(id: Id, actionUser: CurrentUser) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'deleteClass';
      try {
        validateParamsId(id);
        const schoolId = parserId(actionUser.schoolId);
        // First remove students from the class
        const isDeleted = await this.dao.deleteClassById(client, id, schoolId);
        if (!isDeleted) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToDelete')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'deletedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async addStudentsToClassInBulk(
    classId: number,
    studentIds: number[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'addStudentsToClassInBulk';
      try {
        validateParamsId(classId);
        if (!classId)
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'classIdRequired')
          );
        const schoolId = parserId(actionUser.schoolId);
        const successResults = await Promise.all(
          studentIds.map((studentId) =>
            this.dao.updateClassForStudent(
              client,
              schoolId,
              classId,
              studentId,
              actionUser,
              'add'
            )
          )
        );
        const allSucceeded = successResults.every((result) => result === true);
        if (!allSucceeded) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToAdd')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'addedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStudentsFromClassInBulk(
    classId: number,
    studentIds: number[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStudentsFromClassInBulk';
      try {
        validateParamsId(classId);
        const schoolId = parserId(actionUser.schoolId);
        const successResults = await Promise.all(
          studentIds.map((studentId) =>
            this.dao.updateClassForStudent(
              client,
              schoolId,
              classId,
              studentId,
              actionUser,
              'remove'
            )
          )
        );
        const allSucceeded = successResults.every((result) => result === true);
        if (!allSucceeded) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStudentFormClass(
    classId: Id,
    studentId: Id,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStudentFormClass';
      try {
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateClassForStudent(
          client,
          schoolId,
          classId,
          studentId,
          actionUser,
          'remove'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return success;
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async addStaffsToClassInBulk(
    classId: number,
    staffIds: number[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'addStaffsToClassInBulk';
      try {
        validateParamsId(classId);
        const schoolId = parserId(actionUser.schoolId);
        const successResults = await Promise.all(
          staffIds.map((staffId) =>
            this.dao.updateClassForStaff(
              client,
              schoolId,
              classId,
              staffId,
              'add'
            )
          )
        );
        const allSucceeded = successResults.every((result) => result === true);
        if (!allSucceeded) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToAdd')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'addedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStaffsFromClassInBulk(
    classId: number,
    staffIds: number[],
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStaffsFromClassInBulk';
      try {
        validateParamsId(classId);
        const schoolId = parserId(actionUser.schoolId);
        const successResults = await Promise.all(
          staffIds.map((staffId) =>
            this.dao.updateClassForStaff(
              client,
              schoolId,
              classId,
              staffId,
              'remove'
            )
          )
        );
        const allSucceeded = successResults.every((result) => result === true);
        if (!allSucceeded) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }

  async removeStaffFormClass(
    classId: Id,
    staffId: Id,
    actionUser: CurrentUser
  ) {
    return this.txs.withTransaction(async (client) => {
      const messageKey = 'removeStaffFormClass';
      try {
        validateParamsId(classId);
        validateParamsId(staffId);
        const schoolId = parserId(actionUser.schoolId);
        const success = await this.dao.updateClassForStaff(
          client,
          schoolId,
          classId,
          staffId,
          'remove'
        );
        if (!success) {
          throw new HttpException.BadRequest(
            formatErrorResponse(messageKey, 'unableToRemove')
          );
        }
        return messageResponse(
          formatSuccessResponse(messageKey, 'removedSuccessfully')
        );
      } catch (error: Error | any) {
        throw commonCatchHandler(error, messageKey);
      }
    });
  }
}
