import { PoolClient } from 'pg';
import { CreateClassDto, UpdateClassDto } from './types.ts';
import { CurrentUser, Filter, Id } from '../../models/genericTypes.ts';
import Mapper from '../../utils/daoHelper/mapper.ts';
import Queries from '../../utils/daoHelper/queries.ts';
import classUpdateMap from './updateMaps/classUpdateMap.ts';
import { parserId } from '../../utils/daoHelper/typeParser.ts';
import { ParsedFilter } from '../../models/filter.ts';
import Joins from '../../utils/daoHelper/joins.ts';
import QueryBuilder from '../../utils/daoHelper/queryBuilder.ts';

export default class ClassDao {
  async createClass(
    client: PoolClient,
    dto: CreateClassDto,
    actionUser: CurrentUser
  ) {
    const res = await client.query(
      `INSERT INTO classes 
      (name, description, school_id, status, created_by, updated_by) 
      VALUES ($1, $2, $3, $4, $5, $6) RETURNING id`,
      [
        dto.name,
        dto.description,
        dto.schoolId,
        dto.status,
        actionUser.id,
        actionUser.id,
      ]
    );
    const classId = Mapper.getId(res);

    const query = `
      INSERT INTO class_users (user_id, class_id)
      VALUES ($1, $2);
    `;

    await Promise.all(
      dto.teacherIds.map((userId) => client.query(query, [userId, classId]))
    );
    return classId;
  }

  async getClassById(client: PoolClient, classId: Id, schoolId: Id) {
    const query = `
      SELECT
        c.id,
        c.name,
        c.school_id,
        c.description,
        c.status,
        COUNT(DISTINCT s.id) AS student_count,
        COUNT(DISTINCT cu.user_id) AS teacher_count
      FROM classes c
      LEFT JOIN students s ON s.class_id = c.id
      LEFT JOIN class_users cu ON cu.class_id = c.id
      WHERE c.id = $1 AND c.school_id = $2 AND c.status = 'ACTIVE'
      GROUP BY c.id, c.name, c.school_id, c.description, c.status;
    `;
    const res = await client.query(query, [classId, schoolId]);
    return ClassDao.mapClassDetails(res.rows);
  }

  async getClassesList(client: PoolClient, filter: ParsedFilter, schoolId: Id) {
    const resultSelect = `
    SELECT
      c.id,
      c.name,
      c.school_id,
      c.description,
      c.status,
      COUNT(DISTINCT s.id) AS student_count,
      COUNT(DISTINCT cu.user_id) AS teacher_count
  `;

    const fromAndJoins = `
    FROM classes c
    LEFT JOIN students s ON s.class_id = c.id
    LEFT JOIN class_users cu ON cu.class_id = c.id
  `;

    const groupBy = `
    GROUP BY c.id, c.name, c.school_id, c.description, c.status
  `;

    const totalSelect = `SELECT COUNT(DISTINCT c.id) AS full_count`;

    const where: string[] = [];
    const args: any[] = [];

    // School restriction
    where.push(`c.school_id = ?`);
    args.push(schoolId);

    // Filters
    const { filters } = filter;

    if (filters.search) {
      where.push(`(c.name ILIKE ? OR c.description ILIKE ?)`);
      args.push(`%${filters.search}%`, `%${filters.search}%`);
    }

    if (filters.status) {
      where.push(`c.status = ?`);
      args.push(filters.status);
    }

    const whereClause = where.length ? `WHERE ${where.join(' AND ')}` : '';

    // Sorting
    const direction =
      filter.direction?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    const orderField = this.getClassOrderField(filter, direction);

    // Pagination
    args.push(filter.limit, filter.page * filter.limit);

    const resultQb = new QueryBuilder(
      `${resultSelect} ${fromAndJoins} ${whereClause} ${groupBy}`,
      args
    );
    resultQb.append(`ORDER BY ${orderField} LIMIT ? OFFSET ?`, []);

    const totalQb = new QueryBuilder(
      `${totalSelect} ${fromAndJoins} ${whereClause}`,
      args.slice(0, args.length - 2)
    );

    const { sql, args: outArgs } = resultQb.build();
    const { sql: countSql, args: countArgs } = totalQb.build();

    const rowsRes = await client.query(sql, outArgs);
    const totalRes = await client.query(countSql, countArgs);
    const total = Mapper.getTotalCount(totalRes);
    
    const results = Joins.resultMapper(rowsRes, ClassDao.mapClassDetails);
    return Mapper.getPaginatedResponse(
      Mapper.getNewMetaData(filter as Filter, total),
      results
    );
  }

  // Sorting logic for classes
  private getClassOrderField(filter: ParsedFilter, direction: string): string {
    switch (filter.order) {
      case 'name':
        return `LOWER(c.name) ${direction}`;
      case 'status':
        return `LOWER(c.status) ${direction}`;
      case 'studentCount':
        return `student_count ${direction}`;
      case 'teacherCount':
        return `teacher_count ${direction}`;
      default:
        return `c.id ${direction}`;
    }
  }

  async updateClass(client: PoolClient, dto: UpdateClassDto) {
    const { sql, args } = Queries.updaterFor('classes', classUpdateMap, dto);
    const res = await client.query(sql, args);
    return res.rowCount === 1;
  }

  async deleteClassById(client: PoolClient, id: Id, schoolId: Id) {
    const res1 = await client.query(
      'DELETE FROM class_users WHERE class_id = $1',
      [id]
    );
    const res2 = await client.query(
      'DELETE FROM classes WHERE id = $1 AND school_id = $2',
      [id, schoolId]
    );
    return res1.rowCount === 1 && res2.rowCount === 1;
  }

  async findSchoolClass(client: PoolClient, schoolId: number, classId: number) {
    const query = 'SELECT * FROM classes WHERE school_id = $1 AND id = $2';
    const result = await client.query(query, [schoolId, classId]);
    return ClassDao.mapSchoolClass(result.rows);
  }

  async updateClassForStudent(
    client: PoolClient,
    schoolId: number,
    classId: number,
    studentId: number,
    actionUser: CurrentUser,
    type: 'add' | 'remove'
  ) {
    let result = false;
    if (type === 'add') {
      const res = await client.query(
        `UPDATE students
      SET class_id = $1, updated_by = $2
      WHERE id = $3
        AND school_id = $4
        AND status = 'ACTIVE'`,
        [classId, actionUser.id, studentId, schoolId]
      );
      result = res.rowCount === 1;
    } else {
      const res = await client.query(
        `UPDATE students
      SET class_id = null, updated_by = $1
      WHERE id = $2
          AND school_id = $3
          AND status = 'ACTIVE'`,
        [actionUser.id, studentId, schoolId]
      );
      result = res.rowCount === 1;
    }
    return result;
  }

  async updateClassForStaff(
    client: PoolClient,
    schoolId: number,
    classId: number,
    staffId: number,
    type: 'add' | 'remove'
  ): Promise<boolean> {
    let result = false;

    if (type === 'add') {
      const res = await client.query(
        `
      INSERT INTO class_users (class_id, user_id)
      SELECT $1, su.user_id
      FROM school_users su
      WHERE su.user_id = $2 AND su.school_id = $3
      ON CONFLICT (class_id, user_id) DO NOTHING
      `,
        [classId, staffId, schoolId]
      );
      result = res.rowCount === 1;
    } else if (type === 'remove') {
      const res = await client.query(
        `
      DELETE FROM class_users cu
      WHERE cu.class_id = $1
        AND cu.user_id = $2
        AND EXISTS (
          SELECT 1 FROM school_users su
          WHERE su.user_id = cu.user_id
            AND su.school_id = $3
        )
      `,
        [classId, staffId, schoolId]
      );
      result = res.rowCount === 1;
    }

    return result;
  }

  static mapSchoolClass(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      name: firstRow.name,
      description: firstRow.description,
      status: firstRow.status,
    };
  }

  static mapClassDetails(rows: Record<string, any>) {
    const firstRow = rows[0];
    if (!firstRow) return null;
    return {
      id: parserId(firstRow.id),
      schoolId: parserId(firstRow.school_id),
      name: firstRow.name,
      description: firstRow.description,
      status: firstRow.status,
      studentCount: parserId(firstRow.student_count),
      teacherCount: parserId(firstRow.teacher_count),
    };
  }
}
