import Joi from 'joi';
import {
  getEnumArrayFromObj,
  STATUS,
  nullableStringValidator,
  enumValidator,
  stringValidator,
} from '../../../utils/index.ts';

export default Joi.object(
  ((messageKey) => ({
    name: stringValidator(messageKey, 'name'),
    description: nullableStringValidator(messageKey, 'description'),
    status: enumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('updateClass')
).options({ stripUnknown: true });
