import Joi from 'joi';
import {
  requiredStringValidator,
  requiredEnumValidator,
  getEnumArrayFromObj,
  STATUS,
  requiredNumberArrayValidator,
  nullableStringValidator,
} from '../../../utils/index.ts';

export default Joi.object(
  ((messageKey) => ({
    name: requiredStringValidator(messageKey, 'name'),
    description: nullableStringValidator(messageKey, 'description'),
    teacherIds: requiredNumberArrayValidator(messageKey, 'teacherIds'),
    status: requiredEnumValidator(
      getEnumArrayFromObj(STATUS) || [],
      messageKey,
      'status'
    ),
  }))('createClass')
).options({ stripUnknown: true });
