import Right from './right.ts';
import { HttpException } from '../utils/index.ts';
import { formatErrorResponse } from '../utils/apiResponses.ts';

/**
 * Roles of the authorization system.
 * Each user will have one or more {@link Role}s linked to them either directly
 * or via their usergroups. Each {@link Role} contain one
 * or more {@link Right}s which are ultimately determine
 * if user has access to certain features etc.
 *
 * Please note that in addition to right based authorization
 * there is also contextual authorization going on, this means
 * that certain {@link Right}s are contextual in nature: you may
 * be allowed to see some data (eg. students) but your
 * view might be restricted to only specific items
 * (like the students of your own company).
 */

export type RoleName =
  | 'SUPER_ADMIN'
  | 'ADMIN'
  | 'TEACHER'
  | 'MENTOR'
  | 'NO_RIGHTS';

class Role {
  private static readonly roles = Object.freeze<RoleName[]>([
    'SUPER_ADMIN',
    'ADMIN',
    'TEACHER',
    'MENTOR',
    'NO_RIGHTS',
  ]);

  static readonly roleValues = Object.freeze({
    SUPER_ADMIN: 'SUPER_ADMIN' as <PERSON>Name,
    ADMIN: 'ADMIN' as <PERSON>N<PERSON>,
    TEACHER: 'TEACHER' as RoleName,
    MENTOR: 'MENTOR' as RoleName,
  });

  static readonly staffRoleValues = Object.freeze({
    TEACHER: 'TEACHER' as RoleName,
    MENTOR: 'MENTOR' as RoleName,
  });

  static isStaffRoles(roles?: RoleName[]): boolean {
    if (!roles || roles.length === 0) return false;
    const staffRoles = Object.values(Role.staffRoleValues);
    return roles.every((r) => staffRoles.includes(r));
  }
  
  private readonly roleIds = Object.freeze<Record<RoleName, number>>({
    NO_RIGHTS: 0, // optional, to avoid fallback 0
    SUPER_ADMIN: 1,
    ADMIN: 2,
    TEACHER: 3,
    MENTOR: 4,
  });

  private readonly USER: string[] = Right.userRights();

  private readonly NO_RIGHTS: string[] = [];

  private static readonly roleRightsMap: Record<RoleName, string[]> = {
    SUPER_ADMIN: Right.userRights(),
    ADMIN: [],
    TEACHER: [],
    MENTOR: [],
    NO_RIGHTS: [],
  };

  private readonly rights: string[];
  private readonly role: RoleName;

  constructor(role: string) {
    if (!Role.roles.includes(role as RoleName)) {
      throw new HttpException.BadRequest(
        formatErrorResponse('role', 'notFound')
      );
    }

    this.role = role as RoleName;
    this.rights = Role.roleRightsMap[this.role];
  }

  /**
   * Checks if the user has the right
   * @param right
   * @returns boolean
   */
  hasRight(right: string): boolean {
    return this.rights?.includes(right) ?? false;
  }

  /**
   * Gets the rights available for this role
   * @returns string[]
   */
  getRights(): string[] {
    return this.rights;
  }

  /**
   * Gets the role id based on the role
   * @returns number
   */
  getId(): number {
    return this.roleIds[this.role] || 0;
  }

  /**
   * Gets the name of the role
   * @returns string
   */
  getRoleName(): RoleName {
    return this.role;
  }
}

export default Role;
