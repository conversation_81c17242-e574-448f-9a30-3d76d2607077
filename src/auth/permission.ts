type PermissionStaticKeys = {
  [K in keyof typeof Permission]: (typeof Permission)[K] extends Function
    ? never
    : K;
}[Exclude<keyof typeof Permission, 'prototype'>];

export type PermissionName = PermissionStaticKeys;

export default class Permission {
  static GENERAL = Object.freeze({
    LOGIN: 'LOGIN',
    VERIFY_TOKEN: 'VERIFY_TOKEN',
    GET_ROLES: 'GET_ROLES',
    PING: 'PING',
    SCHOOL_SIGNUP: 'SCHOOL_SIGNUP',
  });

  static TWO_FA = Object.freeze({
    GET_TWO_FA_METHODS: 'GET_TWO_FA_METHODS',
    SET_UP_TWO_FA: 'SET_UP_TWO_FA',
    VERIFY_TWO_FA: 'VERIFY_TWO_FA',
    MODIFY_TWO_FA: 'MODIFY_TWO_FA',
    USER_TWO_FA_METHODS: 'USER_TWO_FA_METHODS',
  });

  static PROFILE_MANAGEMENT = Object.freeze({
    GET_PROFILE: 'GET_PROFILE',
    GET_USER_RIGHTS: 'GET_USER_RIGHTS',
    UPDATE_PROFILE: 'UPDATE_PROFILE',
    UPLOAD_PROFILE_PICTURE: 'UPLOAD_PROFILE_PICTURE',
    DELETE_PROFILE_PICTURE: 'DELETE_PROFILE_PICTURE',
    CHANGE_PASSWORD: 'CHANGE_PASSWORD',
    FORGOT_PASSWORD: 'FORGOT_PASSWORD',
    RESET_PASSWORD: 'RESET_PASSWORD',
  });

  static SCHOOL_MANAGEMENT = Object.freeze({
    GET_STAFFS_LIST: 'GET_STAFFS_LIST',
    GET_SCHOOL_BY_ID: 'GET_SCHOOL_BY_ID',
    GET_SCHOOL_ANALYTICS: 'GET_SCHOOL_ANALYTICS',
    UPDATE_SCHOOL_BY_ID: 'UPDATE_SCHOOL_BY_ID',
  });

  static STAFF_MANAGEMENT = Object.freeze({
    CREATE_STAFF: 'CREATE_STAFF',
    CREATE_STAFFS_IN_BULK: 'CREATE_STAFFS_IN_BULK',
    UPDATE_STAFF_BY_ID: 'UPDATE_STAFF_BY_ID',
    DELETE_STAFF_BY_ID: 'DELETE_STAFF_BY_ID',
    GET_STAFFS_LIST: 'GET_STAFFS_LIST',
    GET_STAFF_BY_ID: 'GET_STAFF_BY_ID',
    SEND_CREDENTIALS_TO_STAFF_BY_ID: 'SEND_CREDENTIALS_TO_STAFF_BY_ID',
    SEND_CREDENTIALS_TO_STAFFS_IN_BULK: 'SEND_CREDENTIALS_TO_STAFFS_IN_BULK',
  });

  static CLASS_MANAGEMENT = Object.freeze({
    CREATE_CLASS: 'CREATE_CLASS',
    UPDATE_CLASS_BY_ID: 'UPDATE_CLASS_BY_ID',
    DELETE_CLASS_BY_ID: 'DELETE_CLASS_BY_ID',
    GET_CLASSES_LIST: 'GET_CLASSES_LIST',
    GET_CLASS_BY_ID: 'GET_CLASS_BY_ID',
    ADD_STUDENTS_TO_CLASS_IN_BULK: 'ADD_STUDENTS_TO_CLASS_IN_BULK',
    REMOVE_STUDENTS_FROM_CLASS_IN_BULK: 'REMOVE_STUDENTS_FROM_CLASS_IN_BULK',
    REMOVE_STUDENT_FROM_CLASS: 'REMOVE_STUDENT_FROM_CLASS',
    ADD_STAFFS_TO_CLASS_IN_BULK: 'ADD_STAFFS_TO_CLASS_IN_BULK',
    REMOVE_STAFFS_FROM_CLASS_IN_BULK: 'REMOVE_STAFFS_FROM_CLASS_IN_BULK',
    REMOVE_STAFF_FROM_CLASS: 'REMOVE_STAFF_FROM_CLASS',
  });

  static HOUSE_MANAGEMENT = Object.freeze({
    CREATE_HOUSE: 'CREATE_HOUSE',
    UPDATE_HOUSE_BY_ID: 'UPDATE_HOUSE_BY_ID',
    DELETE_HOUSE_BY_ID: 'DELETE_HOUSE_BY_ID',
    GET_HOUSES_LIST: 'GET_HOUSES_LIST',
    GET_HOUSE_BY_ID: 'GET_HOUSE_BY_ID',
    ADD_STUDENTS_TO_HOUSE_IN_BULK: 'ADD_STUDENTS_TO_HOUSE_IN_BULK',
    REMOVE_STUDENTS_FROM_HOUSE_IN_BULK: 'REMOVE_STUDENTS_FROM_HOUSE_IN_BULK',
    REMOVE_STUDENT_FROM_HOUSE: 'REMOVE_STUDENT_FROM_HOUSE',
  });

  static MENTORING_GROUP_MANAGEMENT = Object.freeze({
    CREATE_MENTORING_GROUP: 'CREATE_MENTORING_GROUP',
    UPDATE_MENTORING_GROUP_BY_ID: 'UPDATE_MENTORING_GROUP_BY_ID',
    DELETE_MENTORING_GROUP_BY_ID: 'DELETE_MENTORING_GROUP_BY_ID',
    GET_MENTORING_GROUPS_LIST: 'GET_MENTORING_GROUPS_LIST',
    GET_MENTORING_GROUP_BY_ID: 'GET_MENTORING_GROUP_BY_ID',
    ADD_STUDENTS_TO_MENTORING_GROUP_IN_BULK:
      'ADD_STUDENTS_TO_MENTORING_GROUP_IN_BULK',
    REMOVE_STUDENTS_FROM_MENTORING_GROUP_IN_BULK:
      'REMOVE_STUDENTS_FROM_MENTORING_GROUP_IN_BULK',
    REMOVE_STUDENT_FROM_MENTORING_GROUP: 'REMOVE_STUDENT_FROM_MENTORING_GROUP',
    ADD_STAFFS_TO_MENTORING_GROUP_IN_BULK:
      'ADD_STAFFS_TO_MENTORING_GROUP_IN_BULK',
    REMOVE_STAFF_TO_MENTORING_GROUP: 'ADD_STAFF_TO_MENTORING_GROUP',
    GET_MENTORING_GROUP_STUDENTS_LIST: 'GET_MENTORING_GROUP_STUDENTS_LIST',
  });

  static STUDENT_MANAGEMENT = Object.freeze({
    CREATE_STUDENT: 'CREATE_STUDENT',
    CREATE_STUDENTS_IN_BULK: 'CREATE_STUDENTS_IN_BULK',
    UPDATE_STUDENT_BY_ID: 'UPDATE_STUDENT_BY_ID',
    DELETE_STUDENT_BY_ID: 'DELETE_STUDENT_BY_ID',
    GET_STUDENTS_LIST: 'GET_STUDENTS_LIST',
    GET_STUDENT_BY_ID: 'GET_STUDENT_BY_ID',
  });

  static SCHOOL_SETTINGS = Object.freeze({
    GET_SCHOOL_BY_ID: 'GET_SCHOOL_BY_ID',
    UPDATE_SCHOOL_CHECK_IN_ASSESSMENT_SETTINGS:
      'UPDATE_SCHOOL_CHECK_IN_ASSESSMENT_SETTINGS',
  });

  static SCHOOL_CHECK_IN_ASSESSMENT_MANAGEMENT = Object.freeze({
    GET_LATEST_SCHOOL_CHECK_IN_ASSESSMENT:
      'GET_LATEST_SCHOOL_CHECK_IN_ASSESSMENT',
    UPDATE_SCHOOL_CHECK_IN_ASSESSMENT_QUESTION_BY_ID:
      'UPDATE_SCHOOL_CHECK_IN_ASSESSMENT_QUESTION_BY_ID',
  });

  static SEND_CHECK_IN_ASSESSMENT = Object.freeze({
    SEND_CHECK_IN_ASSESSMENT_TO_STUDENT_BY_ID:
      'SEND_CHECK_IN_ASSESSMENT_TO_STUDENT_BY_ID',
    SEND_CHECK_IN_ASSESSMENT_TO_STUDENT_IN_BULK:
      'SEND_CHECK_IN_ASSESSMENT_TO_STUDENT_IN_BULK',
  });

  static STUDENT_CHECK_IN_ASSESSMENT_READ_ONLY = Object.freeze({
    GET_STUDENT_CHECK_IN_ASSESSMENT_RESPONSE_LIST:
      'GET_STUDENT_CHECK_IN_ASSESSMENT_RESPONSE_LIST',
    GET_STUDENT_CHECK_IN_ASSESSMENT_RESPONSE_SUMMARY:
      'GET_STUDENT_CHECK_IN_ASSESSMENT_RESPONSE_SUMMARY',
    GET_STUDENTS_CHECK_IN_ASSESSMENT_LAST_RESPONSE_LIST:
      'GET_STUDENT_CHECK_IN_ASSESSMENT_LAST_RESPONSE_LIST',
  });

  static FOLLOW_UPS = Object.freeze({
    GIVE_FOLLOW_UP_ON_STUDENT_CHECK_IN_ASSESSMENT_BY_ID:
      'GIVE_FOLLOW_UP_ON_STUDENT_CHECK_IN_ASSESSMENT_BY_ID',
    GET_FILTERED_STUDENT_FOLLOW_UPS: 'GET_FILTERED_STUDENT_FOLLOW_UPS',
  });

  static getPermissionRights(module: PermissionName) {
    return this[module];
  }
}
