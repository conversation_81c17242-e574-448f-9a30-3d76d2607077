import Handlebars from 'handlebars';
import fs from 'fs';
import path from 'path';

const RESOURCES_DIR = path.resolve('src/resources');

export const parseHbsTemplate = (source: string, data: Record<string, any>): string => {
  const template = Handlebars.compile(source);
  return template(data);
};

export const getResourcesFileSource = (relativePath: string): Promise<string> => {
  return new Promise((resolve, reject) => {
    fs.readFile(path.join(RESOURCES_DIR, relativePath), 'utf8', (err, data) => {
      if (err) {
        reject(err);
      } else {
        resolve(data);
      }
    });
  });
};
