import config from "../config/index.ts";

import sgMail from "@sendgrid/mail";


sgMail.setApiKey(config.sendgrid.apiKey);

export default class EmailClient {
  private fromEmail: string;
  private fromName: string;
  constructor() {
    this.fromEmail = `${config.sendgrid.fromEmail}`;
    this.fromName = `${config.sendgrid.fromName}`;
  }
  
  async sendEmail(subject : string, body : string, to : string) {
    const mailOptions = {
      to,
      from: {
        name: this.fromName,
        email: this.fromEmail,
      },
      subject,
      html: body,
    };

    return sgMail
      .send(mailOptions)
      .then((res) => res)
      .catch((err) => err);
  }
}
