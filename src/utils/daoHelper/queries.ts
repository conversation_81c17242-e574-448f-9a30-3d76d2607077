import QueryBuilder from './queryBuilder.ts';

type UpdateMapper<T> = (_entity: T) => Record<string, any>;
type Transformer<T> = (_elem: T) => any[];

class Queries {
  static updaterFor<T extends Record<string, any>>(
    tableName: string,
    updateMapper: UpdateMapper<T>,
    entity: T,
    uniqueKey: keyof T = 'id'
  ): { sql: string; args: any[] } {
    const qb = new QueryBuilder('UPDATE ').append(tableName).append(' SET ');

    const updateMap = updateMapper(entity);

    if (Object.keys(updateMap).length === 0) {
      return qb
        .append(`id = id WHERE ${String(uniqueKey)} = ?`, [entity[uniqueKey]])
        .build();
    }

    Object.entries(updateMap).forEach(([key, value], i, arr) => {
      qb.append(`${key}=?`, [value]);
      if (i + 1 !== arr.length) qb.append(',');
    });

    return qb
      .append(` WHERE ${String(uniqueKey)} = ?`, [entity[uniqueKey]])
      .build();
  }

  static batchInsert<T>(
    baseSql: string,
    elems: T[],
    transformer: Transformer<T>
  ): { sql: string; args: any[] } {
    const qb = new QueryBuilder(baseSql);
    qb.append(' VALUES ');

    elems.forEach((elem, i) => {
      const args = transformer(elem);
      const placeholders = Array(args.length)
        .fill(QueryBuilder.placeholder)
        .join(',');
      qb.append(`(${placeholders})`, args);
      if (i + 1 !== elems.length) qb.append(',');
    });

    return qb.build();
  }

  static batchInsertWithReturningParameters<T>(
    baseSql: string,
    elems: T[],
    transformer: Function,
    returningParameters: string[]
  ) {
    const qb = new QueryBuilder(baseSql);
    qb.append(' VALUES ');
    elems.forEach((elem, i) => {
      const args = transformer(elem);
      let sql = '(';
      for (let j = 0; j < args.length; j++) {
        sql += `${QueryBuilder.placeholder}`;
        if (j + 1 !== args.length) sql += ',';
      }
      sql += ')';
      qb.append(sql, [...args]);
      if (i + 1 !== elems.length) qb.append(',');
    });
    qb.append(' RETURNING ');
    returningParameters.forEach((elm, i) => {
      if (returningParameters.length === 1) {
        qb.append(`${elm}`);
      } else if (returningParameters.length === i + 1) {
        qb.append(`${elm}`);
      } else {
        qb.append(`${elm},`);
      }
    });
    return qb.build();
  }
}

export default Queries;
