export const STATUS = Object.freeze({
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
});

export const PORTAL = Object.freeze({
  ADMIN_PORTAL: 'ADMIN_PORTAL',
  STUDENT_PORTAL: 'STUDENT_PORTAL',
});

export const ROLE = Object.freeze({
  SUPER_ADMIN: 'SUPER_ADMIN',
  ADMIN: 'ADMIN',
  TEACHER: 'TEACHER',
  MENTOR: 'MENTOR',
});

export const MAPPED_ROLE = Object.freeze({
  SUPER_ADMIN: 1,
  ADMIN: 2,
  TEACHER: 3,
  MENTOR: 4,
});

export const SCHOOL_TYPE = Object.freeze({
  DAY_SCHOOL: 'DAY_SCHOOL',
  BOARDING_SCHOOL: 'BOARDING_SCHOOL',
});

export const MAP_SCHOOL_TYPE = Object.freeze({
  DAY_SCHOOL: 'Day School',
  BOARDING_SCHOOL: 'Boarding School',
});

export const MESSAGE_TYPES = Object.freeze({
  EMAIL: 'EMAIL',
});

export const ACTION_BY_TYPE = Object.freeze({
  SELF: 'SELF',
  ADMIN: 'ADMIN',
});

export const TOKEN_TYPE = Object.freeze({
  LOGIN: 'LOGIN',
  ENABLE: 'ENABLE_2FA',
  DISABLE: 'DISABLE_2FA',
  MODIFY: 'UPDATE_2FA_SETTINGS',
});

export const ACTION_TYPE = Object.freeze({
  LOGIN: 'LOGIN',
  MODIFY: 'MODIFY',
  ENABLE: 'ENABLE',
  DISABLE: 'DISABLE',
});

export const SUCCESS_ACTION_TYPE = Object.freeze({
  LOGIN: 'LOGIN',
  ENABLE_2FA: 'ENABLED',
  DISABLE_2FA: 'DISABLED',
  UPDATE_2FA_SETTINGS: 'UPDATED',
});

export const LOGIN_TYPE = Object.freeze({
  ACCESS_CREDENTIALS: 'ACCESS_CREDENTIALS',
  ACCESS_CREDENTIALS_WITH_2FA: 'ACCESS_CREDENTIALS_WITH_2FA',
});

export const LOGIN_TOKEN_TYPE = Object.freeze({
  OTP: 'OTP',
  RECOVERY_CODE: 'RECOVERY_CODE',
});

export const WEEK_DAYS = Object.freeze({
  MONDAY: 'MONDAY',
  TUESDAY: 'TUESDAY',
  WEDNESDAY: 'WEDNESDAY',
  THURSDAY: 'THURSDAY',
  FRIDAY: 'FRIDAY',
  SATURDAY: 'SATURDAY',
  SUNDAY: 'SUNDAY',
});

export const GENDER_TYPES = Object.freeze({
  MALE: 'MALE',
  FEMALE: 'FEMALE',
  BIGENDER: 'BIGENDER',
  AGENDER: 'AGENDER',
  UNSPECIFIED: 'UNSPECIFIED',
  NON_BINARY: 'NON_BINARY',
  NOT_KNOWN: 'NOT_KNOWN',
});

export const IMAGE_MIMES = [
  'image/jpeg',
  'image/pjpeg',
  'image/png',
  'image/bmp',
  'image/tiff',
  'image/webp',
  'image/avif',
  'image/jpg',
];

export const VIDEO_MIMES = [
  'video/mp4',
  'video/ogg',
  'video/webm',
  'video/x-msvideo',
  'video/quicktime',
  'video/x-flv',
  'video/x-ms-wmv',
  'application/x-mpegURL',
  'video/MP2T',
];

export const IMAGE_VIDEO_MIMES = [...IMAGE_MIMES, ...VIDEO_MIMES];

export const FILE_MIMES = [
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'application/vnd.ms-powerpoint',
  'application/vnd.openxmlformats-officedocument.presentationml.presentation',
  ...IMAGE_MIMES,
  'text/plain',
  'text/csv',
  ...VIDEO_MIMES,
];

export const MAX_FILE_SIZE = 4 * 1024 * 1024;
export const MAX_NUM_FILES = 7;
