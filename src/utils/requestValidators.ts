import Joi from 'joi';
import type { CustomHelpers } from 'joi';
import moment from 'moment';
import {
  joiStringError,
  joiNumberError,
  joiBooleanError,
  joiEmailError,
  joiDateError,
  joiGeneralError,
} from './apiResponses.ts';

type MessageKey = string;
type FieldKey = string;

type DateValidatorOptions = {
  onlyPast?: boolean;
};

// --- ID Validators ---
export const idValidator = () => Joi.number().integer().positive();

export const requiredIdValidator = (messageKey: MessageKey, key: FieldKey) =>
  idValidator().required().messages(joiNumberError(messageKey, key));

export const nullableIdValidator = (messageKey: MessageKey, key: FieldKey) =>
  idValidator().allow(null).messages(joiNumberError(messageKey, key));

// --- Money Validators ---
export const moneyValidator = () => Joi.number().positive().precision(2);

export const requiredMoneyValidator = (messageKey: MessageKey, key: FieldKey) =>
  moneyValidator().required().messages(joiNumberError(messageKey, key));

export const nullableMoneyValidator = (messageKey: MessageKey, key: FieldKey) =>
  moneyValidator().allow(null).messages(joiNumberError(messageKey, key));

// --- String Validators ---
export const stringValidator = (messageKey: MessageKey, key: FieldKey) =>
  Joi.string().messages(joiStringError(messageKey, key));

export const requiredStringValidator = (
  messageKey: MessageKey,
  key: FieldKey
) => Joi.string().required().messages(joiStringError(messageKey, key));

export const nullableStringValidator = (
  messageKey: MessageKey,
  key: FieldKey
) => Joi.string().allow(null).messages(joiStringError(messageKey, key));

// --- Date Validators ---
export const dateValidator = ({ onlyPast }: DateValidatorOptions = {}) => {
  let validator = Joi.date().iso().raw();
  if (onlyPast) {
    validator = validator.max('now');
  }
  return validator;
};

export const requiredDateValidator = (
  messageKey: MessageKey,
  key: FieldKey,
  options?: DateValidatorOptions
) => dateValidator(options).required().messages(joiDateError(messageKey, key));

export const nullableDateValidator = (
  messageKey: MessageKey,
  key: FieldKey,
  options?: DateValidatorOptions
) => dateValidator(options).allow(null).messages(joiDateError(messageKey, key));

// --- Custom End Date Validator ---
export const validateEndDate =
  (startDateKey: string) =>
  (value: any, helpers: CustomHelpers): any => {
    const ancestor = helpers?.state?.ancestors?.[0];
    const startDate = ancestor?.[startDateKey];

    if (startDate && !value) {
      return helpers.error('any.invalid');
    }

    if (startDate && value && moment(startDate).isAfter(moment(value))) {
      return helpers.error('any.invalid');
    }

    return value;
  };

// --- Email Validators ---
export const emailValidator = () => Joi.string().lowercase().email();

export const requiredEmailValidator = (messageKey: MessageKey, key: FieldKey) =>
  emailValidator().required().messages(joiEmailError(messageKey, key));

export const nullableEmailValidator = (messageKey: MessageKey, key: FieldKey) =>
  emailValidator().allow(null).messages(joiEmailError(messageKey, key));

export const stringEmailValidator = (messageKey: MessageKey, key: FieldKey) =>
  emailValidator().messages(joiEmailError(messageKey, key));

// --- Number Validators ---
export const numberValidator = (messageKey: MessageKey, key: FieldKey) =>
  Joi.number().messages(joiNumberError(messageKey, key));

export const requiredNumberValidator = (
  messageKey: MessageKey,
  key: FieldKey
) => Joi.number().required().messages(joiNumberError(messageKey, key));

export const nullableNumberValidator = (
  messageKey: MessageKey,
  key: FieldKey
) => Joi.number().allow(null).messages(joiNumberError(messageKey, key));

// --- Boolean Validators ---
export const booleanValidator = (messageKey: MessageKey, key: FieldKey) =>
  Joi.boolean().messages(joiBooleanError(messageKey, key));

export const requiredBooleanValidator = (
  messageKey: MessageKey,
  key: FieldKey
) => Joi.boolean().required().messages(joiBooleanError(messageKey, key));

// --- Enum Validators ---
export const requiredEnumValidator = (
  options: string[],
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.string()
    .valid(...options)
    .required()
    .messages(joiStringError(messageKey, key));

export const nullableEnumValidator = (
  options: string[],
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.string()
    .valid(...options)
    .allow(null)
    .messages(joiStringError(messageKey, key));

export const enumValidator = (
  options: string[],
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.string()
    .valid(...options)
    .messages(joiStringError(messageKey, key));

// --- Regex Validators ---
export const requiredRegexValidator = (
  pattern: RegExp,
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.string()
    .regex(pattern)
    .required()
    .messages(joiStringError(messageKey, key));

export const nullableRegexValidator = (
  pattern: RegExp,
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.string()
    .regex(pattern)
    .allow(null)
    .messages(joiStringError(messageKey, key));

export const regexValidator = (
  pattern: RegExp,
  messageKey: MessageKey,
  key: FieldKey
) => Joi.string().regex(pattern).messages(joiStringError(messageKey, key));

// --- Array Validators ---
export const requiredNumberArrayValidator = (
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.array()
    .min(1)
    .unique()
    .required()
    .messages(joiNumberError(messageKey, key));

export const nullableNumberArrayValidator = (
  messageKey: MessageKey,
  key: FieldKey
) =>
  Joi.array()
    .min(1)
    .unique()
    .allow(null)
    .optional()
    .messages(joiNumberError(messageKey, key));

export const arrayValidatorWithItem = (allowedItems: string[]) =>
  Joi.array()
    .items(Joi.string().valid(...allowedItems))
    .unique()
    .min(1);

export const requiredArrayValidatorWithItems = (
  allowedItems: string[],
  messageKey: MessageKey,
  key: FieldKey
) =>
  arrayValidatorWithItem(allowedItems)
    .required()
    .messages(joiGeneralError(messageKey, key));

export const nullableArrayValidatorWithItems = (
  allowedItems: string[],
  messageKey: MessageKey,
  key: FieldKey
) =>
  arrayValidatorWithItem(allowedItems)
    .allow(null)
    .optional()
    .messages(joiGeneralError(messageKey, key));
