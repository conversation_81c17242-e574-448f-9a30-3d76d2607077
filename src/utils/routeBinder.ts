/* eslint-disable no-use-before-define */
import express from 'express';
import type { Request, Response, NextFunction } from 'express';

const Router = express.Router;

import {
  HttpException,
  formatErrorResponse,
  HttpMethod,
  isApplicableFeatureLevel,
  featureLevel,
} from './index.ts';
import { verifyToken, verifySchool } from '../routes/middlewares/index.ts';
import { Authentication } from '../auth/index.ts';
import { securityService } from '../modules/security/index.ts';

export const route = Router();

type AsyncCallbackHandler = (
  _req: Request,
  _res: Response,
  _next: NextFunction
) => Promise<any>;
type AsyncMiddlewareHandler = Array<
  (_req: Request, _res: Response, _next: NextFunction) => any
>;

export const publicGet = (
  level: keyof typeof featureLevel,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initPublicRoute(level, path, HttpMethod.get, callback, middlewares);
};

export const publicPost = (
  level: keyof typeof featureLevel,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initPublicRoute(level, path, HttpMethod.post, callback, middlewares);
};

export const publicPut = (
  level: keyof typeof featureLevel,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initPublicRoute(level, path, HttpMethod.put, callback, middlewares);
};

export const publicDelete = (
  level: keyof typeof featureLevel,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initPublicRoute(level, path, HttpMethod.delete, callback, middlewares);
};

export const get = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.get, callback, middlewares);
};

export const post = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.post, callback, middlewares);
};

export const put = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.put, callback, middlewares);
};

export const patch = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.patch, callback, middlewares);
};

export const deleteMethod = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.delete, callback, middlewares);
};

export const head = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.head, callback, middlewares);
};

export const trace = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.trace, callback, middlewares);
};

export const options = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
): void => {
  initRouteWith(level, right, path, HttpMethod.options, callback, middlewares);
};

const methods = ['get', 'post', 'put', 'patch', 'delete'] as const;
const handlerMap = {
  get,
  post,
  put,
  patch,
  delete: deleteMethod,
};

/* eslint-disable no-unused-vars */
type SchoolRouteFn = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  callback: AsyncCallbackHandler,
  middlewares?: AsyncMiddlewareHandler
) => void;

type RouteName<M extends string> = `school${Capitalize<M>}`;
type SchoolRoutes = {
  [K in RouteName<(typeof methods)[number]>]: SchoolRouteFn;
};

const routes = methods.reduce((acc, method) => {
  const fnName =
    `school${method.charAt(0).toUpperCase() + method.slice(1)}` as RouteName<
      typeof method
    >;
  acc[fnName] = (
    level: keyof typeof featureLevel,
    right: string,
    path: string,
    callback: AsyncCallbackHandler,
    middlewares: AsyncMiddlewareHandler = []
  ) => {
    handlerMap[method](level, right, path, callback, [
      verifySchool,
      ...middlewares,
    ]);
  };
  return acc;
}, {} as SchoolRoutes);

export const { schoolGet, schoolPost, schoolPut, schoolPatch, schoolDelete } =
  routes;

const initPublicRoute = (
  level: keyof typeof featureLevel,
  path: string,
  method: keyof typeof HttpMethod,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
) => {
  if (isApplicableFeatureLevel(level)) {
    logInitialization(path, method);
    route[method](path, ...middlewares, async (req, res, next) => {
      try {
        const data = await callback(req, res, next);
        if (data) {
          res.status(200).json(data);
          return;
        }
        throw new HttpException.BadRequest(
          formatErrorResponse('general', 'noDataFound')
        );
      } catch (err) {
        return next(err);
      }
    });
  }
};

const initRouteWith = (
  level: keyof typeof featureLevel,
  right: string,
  path: string,
  method: keyof typeof HttpMethod,
  callback: AsyncCallbackHandler,
  middlewares: AsyncMiddlewareHandler = []
) => {
  if (isApplicableFeatureLevel(level)) {
    logInitialization(path, method);
    route[method](
      path,
      verifyToken as any,
      ...middlewares,
      async (req: Request, res: Response, next: NextFunction) => {
        try {
          const { currentUser } = req;
          if (
            !(
              currentUser &&
              Authentication.hasPermission(currentUser?.rights || [], right)
            )
          ) {
            throw new HttpException.Forbidden(
              formatErrorResponse('authToken', 'notAuthorised')
            );
          }
          const data = await callback(req, res, next);
          if (data) {
            const requestDetails: any = {
              ip: req.ip,
              userAgent: req.headers['user-agent'],
            };
            const updatedToken = securityService.updateToken(
              requestDetails,
              currentUser.id,
              currentUser.tokenAud,
              currentUser.roleIds
            );
            res.setHeader('Authorization', `Bearer ${updatedToken}`);
            res.status(200).json(data);
            return;
          }

          throw new HttpException.BadRequest(
            formatErrorResponse('general', 'noDataFound')
          );
        } catch (err) {
          return next(err);
        }
      }
    );
  }
};

export const logInitialization = (
  path: string,
  method: keyof typeof HttpMethod
) => {
  // eslint-disable-next-line no-console
  console.log(`Initialized route [${method.toUpperCase()} ${path}]`);
};
