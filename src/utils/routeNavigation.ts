const API_ROOT = '/api';
const USER_ROOT = `${API_ROOT}/user`;
const ROLES = `${API_ROOT}/roles`;
const COUNTRIES = `${API_ROOT}/countries`;
const SCHOOL_ROOT = `${API_ROOT}/school`;
const STAFF_ROOT = `${SCHOOL_ROOT}/staff`;
const CLASS_ROOT = `${SCHOOL_ROOT}/class`;
const HOUSE_ROOT = `${SCHOOL_ROOT}/house`;
const MENTOR_GROUP_ROOT = `${SCHOOL_ROOT}/mentor-group`;
const STUDENT_ROOT = `${SCHOOL_ROOT}/student`;

export default Object.freeze({
  ping: `${API_ROOT}/ping`,
  test: {
    TEST_ACTION: `${API_ROOT}/test/`,
  },
  healthCheck: `${API_ROOT}/health-check`,
  emailPath: {
    resetPassword: 'reset-password',
    login: 'login',
  },
  security: {
    SIGN_UP: `${API_ROOT}/signup`,
    LOGIN: `${API_ROOT}/login`,
    VERIFY_TOKEN: `${API_ROOT}/verify-token`,
    FORGOT_PASSWORD: `${API_ROOT}/forgot-password`,
    RESET_PASSWORD: `${API_ROOT}/reset-password`,
    CHANGE_PASSWORD: `${API_ROOT}/change-password`,
  },

  general: {
    GET_ROLES: `${ROLES}/filter`,
    GET_COUNTRIES: `${COUNTRIES}/filter`,
    GET_SCHOOL_TYPES: `${API_ROOT}/school/types`,
  },

  twoFa: {
    GET_TWO_FA_METHODS: `${API_ROOT}/two-factor-authentication-methods`,
    TWO_FA: `${API_ROOT}/user/two-factor-authentication`,
    MODIFY_TWO_FA: `${API_ROOT}/user/two-factor-authentication`,
    VERIFY_TWO_FA: `${API_ROOT}/user/two-factor-authentication/verify`,
    USER_TWO_FA_METHODS: `${API_ROOT}/user/two-factor-authentication`,
  },

  profile: {
    GET_PROFILE: `${USER_ROOT}/profile`,
    UPDATE_PROFILE: `${USER_ROOT}/profile`,
    UPLOAD_PROFILE_PICTURE: `${USER_ROOT}/profile/upload`,
    DELETE_PROFILE_PICTURE: `${USER_ROOT}/profile/remove`,
    GET_USER_RIGHTS: `${USER_ROOT}/rights`,
    CHANGE_PASSWORD: `${API_ROOT}/change-password`,
  },

  staffManagement: {
    CREATE_STAFF: `${STAFF_ROOT}`,
    CREATE_STAFFS_IN_BULK: `${SCHOOL_ROOT}/staffs/bulk`,
    UPDATE_STAFF_BY_ID: `${STAFF_ROOT}/:id`,
    DELETE_STAFF_BY_ID: `${STAFF_ROOT}/:id`,
    GET_STAFFS_LIST: `${SCHOOL_ROOT}/staffs/filter`,
    GET_STAFF_BY_ID: `${STAFF_ROOT}/:id`,
    SEND_CREDENTIALS_TO_STAFF_BY_ID: `${STAFF_ROOT}/:id/send-credentials`,
    SEND_CREDENTIALS_TO_STAFFS_IN_BULK: `${SCHOOL_ROOT}/staffs/send-credentials/bulk`,
  },

  classManagement: {
    CREATE_CLASS: `${CLASS_ROOT}`,
    UPDATE_CLASS_BY_ID: `${CLASS_ROOT}/:id`,
    DELETE_CLASS_BY_ID: `${CLASS_ROOT}/:id`,
    GET_CLASSES_LIST: `${SCHOOL_ROOT}/classes/filter`,
    GET_CLASS_BY_ID: `${CLASS_ROOT}/:id`,

    // Students
    ADD_STUDENTS_TO_CLASS_IN_BULK: `${CLASS_ROOT}/:id/students/bulk`,
    REMOVE_STUDENTS_FROM_CLASS_IN_BULK: `${CLASS_ROOT}/:id/students/bulk`,
    REMOVE_STUDENT_FROM_CLASS: `${CLASS_ROOT}/:id/student/:studentId`,

    // Staffs
    ADD_STAFFS_TO_CLASS_IN_BULK: `${CLASS_ROOT}/:id/staffs/bulk`,
    REMOVE_STAFFS_FROM_CLASS_IN_BULK: `${CLASS_ROOT}/:id/staffs/bulk`,
    REMOVE_STAFF_FROM_CLASS: `${CLASS_ROOT}/:id/staff/:staffId`,
  },

  houseManagement: {
    CREATE_HOUSE: `${HOUSE_ROOT}`,
    UPDATE_HOUSE_BY_ID: `${HOUSE_ROOT}/:id`,
    DELETE_HOUSE_BY_ID: `${HOUSE_ROOT}/:id`,
    GET_HOUSES_LIST: `${SCHOOL_ROOT}/houses/filter`,
    GET_HOUSE_BY_ID: `${HOUSE_ROOT}/:id`,

    // Students
    ADD_STUDENTS_TO_HOUSE_IN_BULK: `${HOUSE_ROOT}/:id/students/bulk`,
    REMOVE_STUDENTS_FROM_HOUSE_IN_BULK: `${HOUSE_ROOT}/:id/students/bulk`,
    REMOVE_STUDENT_FROM_HOUSE: `${HOUSE_ROOT}/:id/student/:studentId`,
  },

  mentoringGroupManagement: {
    CREATE_MENTORING_GROUP: `${MENTOR_GROUP_ROOT}`,
    UPDATE_MENTORING_GROUP_BY_ID: `${MENTOR_GROUP_ROOT}/:id`,
    DELETE_MENTORING_GROUP_BY_ID: `${MENTOR_GROUP_ROOT}/:id`,
    GET_MENTORING_GROUPS_LIST: `${SCHOOL_ROOT}/mentor-groups/filter`,
    GET_MENTORING_GROUP_BY_ID: `${MENTOR_GROUP_ROOT}/:id`,

    // Students
    ADD_STUDENTS_TO_MENTORING_GROUP_IN_BULK: `${MENTOR_GROUP_ROOT}/:id/students/bulk`,
    REMOVE_STUDENTS_FROM_MENTORING_GROUP_IN_BULK: `${MENTOR_GROUP_ROOT}/:id/students/bulk`,
    REMOVE_STUDENT_FROM_MENTORING_GROUP: `${MENTOR_GROUP_ROOT}/:id/student/:studentId`,

    // Staffs
    ADD_STAFFS_TO_MENTORING_GROUP_IN_BULK: `${MENTOR_GROUP_ROOT}/:id/staffs/bulk`,
    REMOVE_STAFFS_FROM_MENTORING_GROUP_IN_BULK: `${MENTOR_GROUP_ROOT}/:id/staffs/bulk`,
    REMOVE_STAFF_FROM_MENTORING_GROUP: `${MENTOR_GROUP_ROOT}/:id/staff/:staffId`,
  },

  studentManagement: {
    CREATE_STUDENT: `${STUDENT_ROOT}`,
    CREATE_STUDENTS_IN_BULK: `${SCHOOL_ROOT}/students/bulk`,
    UPDATE_STUDENT_BY_ID: `${STUDENT_ROOT}/:id`,
    DELETE_STUDENT_BY_ID: `${STUDENT_ROOT}/:id`,
  },
});
