import MessageSendingService from './messageSendingService.ts';
import { EmailMessage } from '../models/index.ts';
import config from '../config/index.ts';
import { sanitizeUrl, MESSAGE_TYPES, routes, PORTAL } from '../utils/index.ts';
import { TokenDetails } from '../modules/twoFa/types.ts';
import { MappedUser } from '../modules/userManagement/types.ts';

export type User = {
  firstName?: string | null;
  email: string;
  actualPassword?: string;
};

export type Portal = 'STUDENT_PORTAL' | 'ADMIN_PORTAL';

interface TokenDTO {
  token: string;
  validitySeconds: number | null;
}

class MessageService {
  static PASSWORD_RESET_EMAIL = '/templates/email/password-reset.hbs';
  static INVITE_USER_EMAIL = '/templates/email/invite-user.hbs';
  static MAX_TOKEN_VALIDITY = 3600;
  static TWO_FA_CONFIRAMTION_EMAIL = '/templates/email/two-fa-confirmation.hbs';
  static PASSWORD_UPDATE_EMAIL = '/templates/email/password-update.hbs';
  static SUCCESSFUL_RESET_PASSWORD =
    '/templates/email/successfull-reset-password.hbs';

  static getPortalUrl(portal: Portal) {
    let portalUrl;
    if (portal === PORTAL.ADMIN_PORTAL) {
      portalUrl = sanitizeUrl(config.sendgrid.domain.adminPortal);
    } else {
      portalUrl = sanitizeUrl(config.sendgrid.domain.studentWellBeingProtal);
    }
    return portalUrl;
  }

  static async sendPasswordReset(
    user: MappedUser,
    tokenDto: TokenDTO,
    portal: Portal
  ): Promise<boolean> {
    const subject = 'Reset Password';
    const data = {
      firstName: user?.firstName,
      url: `${MessageService.getPortalUrl(portal)}/${routes.emailPath.resetPassword}/${tokenDto.token}`,
      validity:
        (tokenDto?.validitySeconds || MessageService.MAX_TOKEN_VALIDITY) / 60,
    };
    const message = new EmailMessage(
      subject,
      MessageService.PASSWORD_RESET_EMAIL,
      data
    );
    const res = await MessageService.sendEmail(message, user?.email);
    return res;
  }

  static async sendTwoFaConfirmationCode(user: User, tokenDto: TokenDetails) {
    const subject = '2FA Confirmation';
    const data = {
      firstName: user.firstName,
      code: tokenDto.token,
      validity: tokenDto.validitySecs / 60,
    };
    const message = new EmailMessage(
      subject,
      MessageService.TWO_FA_CONFIRAMTION_EMAIL,
      data
    );
    return await MessageService.sendEmail(message, user.email);
  }

  static async sendPasswordResetConfirmation(user: MappedUser, portal: Portal) {
    try {
      const subject = 'Your password has been reset';
      const data = {
        firstName: user.firstName,
        loginUrl: `${MessageService.getPortalUrl(portal)}/${routes.emailPath.login}`,
      };
      const message = new EmailMessage(
        subject,
        MessageService.SUCCESSFUL_RESET_PASSWORD,
        data
      );
      return await MessageService.sendEmail(message, user.email);
    } catch (err) {
      console.log(err);
    }
  }

  static async sendPasswordChangeEmail(user: User) {
    const subject = 'Password update';
    const data = {
      firstName: user.firstName,
      newPassword: user.actualPassword,
    };
    const message = new EmailMessage(
      subject,
      MessageService.PASSWORD_UPDATE_EMAIL,
      data
    );
    return await MessageService.sendEmail(message, user.email);
  }

  static async sendEmail(
    message: EmailMessage,
    email: string
  ): Promise<boolean> {
    try {
      await MessageSendingService.sendMessage(
        message,
        email,
        MESSAGE_TYPES.EMAIL
      );
      return true;
    } catch (err: any) {
      console.log(`Error sending message for ${email}: ${err.message}`);
    }
    return false;
  }

  static async sendAddUserConfirmationEmail(user: User, portal: Portal) {
    try {
      const subject = 'Welcome To Student Well being Hub';
      const data = {
        name: user.firstName,
        password: user.actualPassword,
        email: user.email,
        url: `${MessageService.getPortalUrl(portal)}/${routes.emailPath.login}`,
      };
      const message = new EmailMessage(
        subject,
        MessageService.INVITE_USER_EMAIL,
        data
      );
      await MessageService.sendEmail(message, user.email);
      return true;
    } catch (error: any) {
      console.log(`Error sending message for ${user.email}: ${error.message}`);
    }
    return false;
  }
}

export default MessageService;
