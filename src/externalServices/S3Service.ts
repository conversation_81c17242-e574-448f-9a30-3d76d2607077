// import path from 'path';
// import {
// //   S3Client,
//   HttpException,
//   formatErrorResponse,
//   deleteFile,
// } from '../utils/index.ts';
// import moment from 'moment';

export default class S3Service {
//   static UPLOADS = 'uploads/';

//   static PROFILE_PICTURE = 'profile/';

//   static async uploadToS3(
//     fileDto: Express.Multer.File,
//     folder = S3Service.UPLOADS
//   ) {
//     const metaData = {
//       name: fileDto.name || fileDto.filename,
//       mimeType: fileDto.mimeType || fileDto.mimetype,
//       fileSize: fileDto.fileSize || fileDto.size,
//       encoding: fileDto.encoding,
//       originalName: fileDto.originalname,
//     };
//     const key = `${folder}${metaData.name}`;
//     const uploadFile = await S3Client.uploadFile({ ...fileDto, key }, metaData);
//     try {
//       await deleteFile(fileDto.path);
//     } catch (error) {
//       // eslint-disable-line no-console
//       console.log(error);
//     }
//     return {
//       ...uploadFile,
//       key: uploadFile.key || uploadFile.Key,
//       ...metaData,
//     };
//   }

//   static async removeFromS3(key: string) {
//     return S3Client.deleteFile(key);
//   }
}
