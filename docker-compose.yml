version: "3.7"
services:
  api:
      image: node:23.11.0-alpine
      container_name: [project_name]_api
      working_dir: /[project_name]
      entrypoint: ["/bin/bash", "./scripts/startup.sh"]
      volumes:
          - ./:/[project_name]
      depends_on:
          - db
      ports:
        - "8080:8080"
  db:
      image: postgres:14
      container_name: [project_name]_db
      restart: always
      ports:
          - "5427:5432"
      environment:
          POSTGRES_USER: [project_name]
          POSTGRES_PASSWORD: password
          POSTGRES_DB: [project_name]-db
      volumes:
        - ./[project_name]-db:/var/lib/postgresql/data