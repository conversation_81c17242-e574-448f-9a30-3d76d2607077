{"compilerOptions": {"lib": ["esnext"], "target": "ES2022", "module": "NodeNext", "moduleResolution": "NodeNext", "rootDir": "src", "outDir": "dist", "strict": true, "esModuleInterop": true, "allowImportingTsExtensions": true, "noEmit": true, "resolveJsonModule": true, "allowJs": true, "forceConsistentCasingInFileNames": true, "skipLibCheck": true}, "ts-node": {"esm": true, "experimentalSpecifierResolution": "node"}, "include": ["src"], "exclude": ["node_modules", "dist", "tests"]}