{"name": "express-pg-api", "type": "module", "version": "1.0.0", "description": "express pg api's boiler plate template", "main": "app.ts", "scripts": {"start": "nodemon --exec tsx src/app.ts", "clean": "rm -Rf ./dist", "build": "npm run clean && tsc", "serve": "node ./dist/app.js", "serve:prod": "pm2-runtime ./dist/app.js -- --migrate migrate", "db:migrate": "db-migrate up", "db:migrate-down": "db-migrate down", "db:clean": "db-migrate reset", "db:refresh": "npm run db:clean && npm run db:migrate", "create:migration": "db-migrate create", "lint": "eslint . --fix --color", "test": "NODE_ENV=test jest --watchAll --colors --verbose --detectOpenHandles --coverage", "import:questionnaire": "node migrations/question_importer/index.js"}, "dependencies": {"@jest/globals": "^29.7.0", "@sendgrid/mail": "^8.1.5", "bcrypt": "^6.0.0", "bluebird": "^3.7.2", "body-parser": "^2.2.0", "convict": "^6.2.4", "cors": "^2.8.5", "db-migrate": "1.0.0-beta.31", "db-migrate-pg": "^1.5.2", "dotenv": "^16.5.0", "express": "^5.1.0", "handlebars": "^4.7.8", "helmet": "^8.1.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "md5": "^2.3.0", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cmd": "^5.0.0", "node-fetch": "^3.3.2", "pg": "^8.16.0", "qs": "^6.14.0", "typedi": "^0.8.0", "uuid": "^11.1.0", "yargs": "^17.7.2"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/core": "^7.27.1", "@babel/eslint-parser": "^7.27.1", "@babel/node": "^7.27.1", "@babel/preset-env": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@eslint/js": "^9.30.1", "@testcontainers/postgresql": "^10.28.0", "@types/bcrypt": "^5.0.2", "@types/bluebird": "^3.5.42", "@types/body-parser": "^1.19.5", "@types/convict": "^6.1.6", "@types/cors": "^2.8.18", "@types/express": "^5.0.1", "@types/helmet": "^4.0.0", "@types/jest": "^29.5.14", "@types/joi": "^17.2.3", "@types/jsonwebtoken": "^9.0.9", "@types/md5": "^2.3.5", "@types/moment": "^2.13.0", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.12", "@types/node": "^22.15.18", "@types/node-cmd": "^5.0.0", "@types/node-fetch": "^2.6.12", "@types/pg": "^8.15.1", "@types/uuid": "^10.0.0", "@types/yargs": "^17.0.33", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "babel-jest": "^29.7.0", "eslint": "^8.57.1", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-import": "^2.32.0", "eslint-plugin-jsdoc": "^50.6.14", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-watch": "^8.0.0", "globals": "^16.3.0", "jest": "^29.7.0", "nodemon": "^3.1.10", "prettier": "^3.6.2", "testcontainers": "^10.28.0", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "tsx": "^4.20.3", "typescript": "^5.8.3"}}