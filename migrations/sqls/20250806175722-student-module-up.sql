-----------
-- Schools
CREATE TABLE students (
  id BIGSERIAL PRIMARY KEY,
  code VARCHAR(225) UNIQUE NOT NULL,
  school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE CASCADE,
  class_id BIGINT REFERENCES classes ON UPDATE CASCADE ON DELETE CASCADE,
  house_id BIGINT REFERENCES houses ON UPDATE CASCADE ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  first_name VARCHAR(70) NOT NULL,
  last_name VARCHAR(70) NOT NULL,
  gender VARCHAR(70),
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_students_modtime BEFORE 
UPDATE ON students 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

-----------
-- Student Mentoring Groups
CREATE TABLE student_mentoring_groups (
  student_id BIGINT NOT NULL REFERENCES students ON UPDATE CASCADE ON DELETE CASCADE,
  mentoring_group_id BIGINT NOT NULL REFERENCES mentoring_groups ON UPDATE CASCADE ON DELETE CASCADE,
  PRIMARY KEY (student_id, mentoring_group_id)
);
