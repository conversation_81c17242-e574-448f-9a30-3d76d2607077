-----------
-- Student Check In Assessment
CREATE TABLE student_check_in_assessments (
  id BIGSERIAL PRIMARY KEY,
  student_id BIGINT NOT NULL REFERENCES students ON UPDATE CASCADE ON DELETE CASCADE,
  code VARCHAR(255) UNIQUE NOT NULL,
  sent_by BIGINT NOT NULL,
  sent_on TIMESTAMPTZ,
  started_on TIMESTAMPTZ,
  completed_on TIMESTAMPTZ,
  follow_up_status VARCHAR(30) NOT NULL,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status VARCHAR(30) NOT NULL DEFAULT 'NOT_STARTED',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

-----------
-- Student Check In Assessment Visibility
CREATE TABLE student_check_in_assessment_visibility (
  id BIGSERIAL PRIMARY KEY,
  student_check_in_assessment_id BIGINT NOT NULL REFERENCES student_check_in_assessments ON UPDATE CASCADE ON DELETE CASCADE,
  user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE CASCADE,
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL
);

-----------
-- Student Check In Assessment Action Logs
CREATE TABLE student_check_in_assessment_action_logs (
  id BIGSERIAL PRIMARY KEY,
  student_check_in_assessment_id BIGINT NOT NULL REFERENCES student_check_in_assessments ON UPDATE CASCADE ON DELETE CASCADE,
  action_type VARCHAR(30) NOT NULL,
  actioned_on TIMESTAMPTZ,
  actioned_by BIGSERIAL NOT NULL,
  log TEXT,
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL
);

---------------------------STUDENT ASSESSMENT QUESTIONNAIRE------------------------

-- Student Check In Assessment Action Logs
CREATE TABLE student_assessment_questionnaires (
  id BIGSERIAL PRIMARY KEY,
  student_check_in_assessment_id BIGINT NOT NULL REFERENCES student_check_in_assessments ON UPDATE CASCADE ON DELETE CASCADE,
  school_assessment_id BIGINT NOT NULL REFERENCES school_assessments ON UPDATE CASCADE ON DELETE CASCADE,
  title VARCHAR(255)NOT NULL,
  description TEXT,
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TABLE student_assessment_questionnaire_questions (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_id BIGINT NOT NULL REFERENCES student_assessment_questionnaires ON UPDATE CASCADE ON DELETE CASCADE,
    code VARCHAR(255) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    response JSON,
    mathematical_equivalent DECIMAL,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_assessment_questionnaire_steps (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_id BIGINT NOT NULL REFERENCES student_assessment_questionnaires ON UPDATE CASCADE ON DELETE CASCADE,
    code VARCHAR(255) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    display_order INTEGER NOT NULL,
    navigation_type VARCHAR(50) NOT NULL DEFAULT 'QUESTIONNAIRE',
    display_type VARCHAR(50) NOT NULL,
    completion_status VARCHAR(50) NOT NULL DEFAULT 'PENDING',
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_assessment_questionnaire_step_sections (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_id BIGINT NOT NULL REFERENCES student_assessment_questionnaires ON UPDATE CASCADE ON DELETE CASCADE,
    code VARCHAR(255) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_assessment_questionnaire_static_elements (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_id BIGINT NOT NULL REFERENCES student_assessment_questionnaires ON UPDATE CASCADE ON DELETE CASCADE,
    code VARCHAR(255) NOT NULL,
    element_type VARCHAR(30)NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_assessment_questionnaire_step_layouts (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_step_id BIGINT NOT NULL REFERENCES student_assessment_questionnaire_steps ON UPDATE CASCADE ON DELETE CASCADE,
    child_id BIGINT NOT NULL,
    child_type VARCHAR(30) NOT NULL DEFAULT 'SECTION',
    layout_configuration JSON,
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_assessment_questionnaire_step_section_layouts (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_step_section_id BIGINT NOT NULL REFERENCES student_assessment_questionnaire_step_sections ON UPDATE CASCADE ON DELETE CASCADE,
    child_id BIGINT NOT NULL,
    child_type VARCHAR(30) NOT NULL DEFAULT 'QUESTION',
    layout_configuration JSON,
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_assessment_results (
    id BIGSERIAL PRIMARY KEY,
    student_assessment_questionnaire_id BIGINT NOT NULL REFERENCES student_assessment_questionnaires ON UPDATE CASCADE ON DELETE CASCADE,
    score INT NOT NULL,
    reserve1 VARCHAR(1), -- reserved column	
    reserve2 VARCHAR(1), -- reserved column	
    reserve3 VARCHAR(1), -- reserved column	
    reserve4 VARCHAR(1), -- reserved column		
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT
);

CREATE TABLE student_check_in_assessment_follow_ups (
    id BIGSERIAL PRIMARY KEY,
    student_check_in_assessment_id BIGINT NOT NULL REFERENCES student_check_in_assessments ON UPDATE CASCADE ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE CASCADE,
    note TEXT NOT NULL,
    reserve1 VARCHAR(1), -- reserved column	
    reserve2 VARCHAR(1), -- reserved column	
    reserve3 VARCHAR(1), -- reserved column	
    reserve4 VARCHAR(1), -- reserved column		
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

CREATE TABLE student_check_in_assessment_escalations (
    id BIGSERIAL PRIMARY KEY,
    student_check_in_assessment_id BIGINT NOT NULL REFERENCES student_check_in_assessments ON UPDATE CASCADE ON DELETE CASCADE,
    user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE CASCADE,
    notify_users JSON NOT NULL,
    reserve1 VARCHAR(1), -- reserved column	
    reserve2 VARCHAR(1), -- reserved column	
    reserve3 VARCHAR(1), -- reserved column	
    reserve4 VARCHAR(1), -- reserved column	
    status VARCHAR(30) NOT NULL,	
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);
