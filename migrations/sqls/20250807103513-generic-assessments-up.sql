--------------------------Assessments-----------------------

-- Creates assessments table
CREATE TABLE assessments (
    id BIGSERIAL PRIMARY KEY,
    code VARCHAR(255) UNIQUE NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(70) NOT NULL,
    title VARCHAR(255),
    description TEXT,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

---------------------------ASSESSMENT-QUESTIONNAIRE-LAYOUT/ALGORITHM------------------------

-- Creates assessments questions table
CREATE TABLE assessment_questions (
    id BIGSERIAL PRIMARY KEY,
    assessment_id BIGINT NOT NULL REFERENCES assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) UNIQUE NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates steps for assessment
CREATE TABLE assessment_steps (
    id BIGSERIAL PRIMARY KEY,
    assessment_id BIGINT NOT NULL REFERENCES assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) UNIQUE NOT NULL,
    visibility_configuration JSON,
    display_configuration JSON,
    display_order INTEGER NOT NULL,
    navigation_type VARCHAR(50) NOT NULL DEFAULT 'QUESTIONNAIRE',
    display_type VARCHAR(50) NOT NULL,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates sections 
CREATE TABLE assessment_step_sections (
    id BIGSERIAL PRIMARY KEY,
    assessment_id BIGINT NOT NULL REFERENCES assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) UNIQUE NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates static elements for the assessments 
CREATE TABLE assessment_static_elements (
    id BIGSERIAL PRIMARY KEY,
    assessment_id BIGINT NOT NULL REFERENCES assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) UNIQUE NOT NULL,
    element_type VARCHAR(30) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates layout for steps
CREATE TABLE assessment_step_layouts (
    id BIGSERIAL PRIMARY KEY,
    assessment_step_id BIGINT NOT NULL REFERENCES assessment_steps ON UPDATE CASCADE ON DELETE RESTRICT,
    child_id BIGINT NOT NULL,
    child_type VARCHAR(30) NOT NULL DEFAULT 'SECTION',
    layout_configuration JSON,
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates layout for sections
CREATE TABLE assessment_step_section_layouts (
    id BIGSERIAL PRIMARY KEY,
    assessment_step_section_id BIGINT NOT NULL REFERENCES assessment_step_sections ON UPDATE CASCADE ON DELETE RESTRICT,
    child_id BIGINT NOT NULL,
    child_type VARCHAR(30) NOT NULL DEFAULT 'QUESTION',
    layout_configuration JSON,
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

--------------------------CLONED SCHOOL ASSESSMENTS-----------------------

-- Creates cloned assessments table
CREATE TABLE school_assessments (
    id BIGSERIAL PRIMARY KEY,
    school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) NOT NULL,
    name VARCHAR(70) NOT NULL,
    title VARCHAR(255),
    description TEXT,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

---------------------------CLONED ASSESSMENT-QUESTIONNAIRE-LAYOUT------------------------
-- Creates cloned assessments questions table
CREATE TABLE school_assessment_questions (
    id BIGSERIAL PRIMARY KEY,
    school_assessment_id BIGINT NOT NULL REFERENCES school_assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates steps for cloned assessment
CREATE TABLE school_assessment_steps (
    id BIGSERIAL PRIMARY KEY,
    school_assessment_id BIGINT NOT NULL REFERENCES school_assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    display_order INTEGER NOT NULL,
    navigation_type VARCHAR(50) NOT NULL DEFAULT 'QUESTIONNAIRE',
    display_type VARCHAR(50) NOT NULL,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates cloned assesment step sections
CREATE TABLE school_assessment_step_sections (
    id BIGSERIAL PRIMARY KEY,
    school_assessment_id BIGINT NOT NULL REFERENCES school_assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates static elements for the school assessments
CREATE TABLE school_assessment_static_elements (
    id BIGSERIAL PRIMARY KEY,
    school_assessment_id BIGINT NOT NULL REFERENCES school_assessments ON UPDATE CASCADE ON DELETE RESTRICT,
    code VARCHAR(255) NOT NULL,
    element_type VARCHAR(30) NOT NULL,
    display_configuration JSON,
    visibility_configuration JSON,
    status status_enum NOT NULL DEFAULT 'ACTIVE',
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates layout for cloned assessment steps
CREATE TABLE school_assessment_step_layouts (
    id BIGSERIAL PRIMARY KEY,
    school_assessment_step_id BIGINT NOT NULL REFERENCES school_assessment_steps ON UPDATE CASCADE ON DELETE RESTRICT,
    child_id BIGINT NOT NULL,
    child_type VARCHAR(30) NOT NULL DEFAULT 'SECTION',
    layout_configuration JSON,
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);

-- Creates layout for cloned assessment step sections
CREATE TABLE school_assessment_step_section_layouts (
    id BIGSERIAL PRIMARY KEY,
    school_assessment_step_section_id BIGINT NOT NULL REFERENCES school_assessment_step_sections ON UPDATE CASCADE ON DELETE RESTRICT,
    child_id BIGINT NOT NULL,
    child_type VARCHAR(30) NOT NULL DEFAULT 'QUESTION',
    layout_configuration JSON,
    created_on TIMESTAMPTZ DEFAULT current_timestamp,
    created_by BIGINT,
    updated_on TIMESTAMPTZ DEFAULT current_timestamp,
    updated_by BIGINT
);
