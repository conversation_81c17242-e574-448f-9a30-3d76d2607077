-----------
-- Countries
CREATE TABLE countries (
  id BIGSERIAL PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) UNIQUE NOT NULL,
  iso_code CHAR(10) UNIQUE NOT NULL,
  dial_code VARCHAR(5) NOT NULL,
  currency_code CHAR(5) NOT NULL,
  currency_symbol VARCHAR(5) NOT NULL,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_countries_modtime BEFORE 
UPDATE ON countries 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

-----------
-- School Types
CREATE TABLE school_types (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_school_types_modtime BEFORE 
UPDATE ON school_types 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

-----------
-- Schools
CREATE TABLE schools (
  id BIGSERIAL PRIMARY KEY,
  name VARCHAR(255) UNIQUE NOT NULL,
  code VARCHAR(17) UNIQUE NOT NULL,
  school_type_id BIGINT NOT NULL REFERENCES school_types ON UPDATE CASCADE ON DELETE CASCADE,
  country_id BIGINT NOT NULL REFERENCES countries ON UPDATE CASCADE ON DELETE CASCADE,
  city VARCHAR(255),
  postal_code VARCHAR(17),
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_schools_modtime BEFORE 
UPDATE ON schools 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

CREATE TABLE school_users (
  user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE CASCADE,
  school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE CASCADE,
  is_owner BOOLEAN NOT NULL DEFAULT FALSE,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column	
  PRIMARY KEY (user_id, school_id)
);

-----------
-- School Settings
CREATE TABLE school_settings (
  id BIGSERIAL PRIMARY KEY,
  school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE CASCADE,
  weekly_check_in_cron_enabled BOOLEAN NOT NULL DEFAULT FALSE,
  weekly_check_in_cron_day VARCHAR(10) NOT NULL DEFAULT 'MONDAY',
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_school_settings_modtime BEFORE 
UPDATE ON school_settings 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

-----------
-- Classes
CREATE TABLE classes (
  id BIGSERIAL PRIMARY KEY,
  school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_classes_modtime BEFORE 
UPDATE ON classes 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

CREATE TABLE class_users (
  user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE CASCADE,
  class_id BIGINT NOT NULL REFERENCES classes ON UPDATE CASCADE ON DELETE CASCADE,
  PRIMARY KEY (user_id, class_id)
);

-----------
-- Houses
CREATE TABLE houses (
  id BIGSERIAL PRIMARY KEY,
  school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_houses_modtime BEFORE 
UPDATE ON houses 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();

-----------
-- Mentoring Groups
CREATE TABLE mentoring_groups (
  id BIGSERIAL PRIMARY KEY,
  school_id BIGINT NOT NULL REFERENCES schools ON UPDATE CASCADE ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  reserve1 VARCHAR(1), -- reserved column	
  reserve2 VARCHAR(1), -- reserved column	
  reserve3 VARCHAR(1), -- reserved column	
  reserve4 VARCHAR(1), -- reserved column		
  status status_enum NOT NULL DEFAULT 'ACTIVE',
  created_on TIMESTAMPTZ DEFAULT current_timestamp,
  created_by BIGINT NOT NULL,
  updated_on TIMESTAMPTZ DEFAULT current_timestamp,
  updated_by BIGINT NOT NULL
);

CREATE TRIGGER update_mentoring_groups_modtime BEFORE 
UPDATE ON mentoring_groups 
FOR EACH ROW EXECUTE PROCEDURE update_updated_on_column ();


CREATE TABLE mentoring_group_users (
  user_id BIGINT NOT NULL REFERENCES users ON UPDATE CASCADE ON DELETE CASCADE,
  mentoring_group_id BIGINT NOT NULL REFERENCES mentoring_groups ON UPDATE CASCADE ON DELETE CASCADE,
  PRIMARY KEY (user_id, mentoring_group_id)
);

-- Insert default countries
INSERT INTO countries 
(name, iso_code, dial_code, currency_code, currency_symbol, status, created_by,updated_by) 
VALUES ('Australia', 'AUS', '+61', 'AUD', '$', 'ACTIVE', 0, 0 );

-- Insert default school types
INSERT INTO school_types 
(name, status, created_by,updated_by) 
VALUES 
  ('Day School', 'ACTIVE', 0, 0 ),
  ('Boarding School', 'ACTIVE', 0, 0 );
