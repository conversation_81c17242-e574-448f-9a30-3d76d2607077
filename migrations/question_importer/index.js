const { Pool, Client } = require('pg');
const fs = require('fs');
const path = require('path');
require('dotenv').config(); 

const cred = {
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  database: process.env.DB_NAME,
  password: process.env.DB_PASSWORD
};


console.log('cred', cred);


const loadAllData = () => {
  const dataFiles = [
    'assessments.json',
    'questions.json',
    // 'staticElements.json',
    // 'section_layout.json',
    'step_layout.json',
  ];

  const data = dataFiles.map(file => {
    const filePath = path.resolve(__dirname, file);
    console.log(`Loading data from ${filePath}...`);
    try {
      const fileData = fs.readFileSync(filePath, 'utf8'); // Correct usage
      return JSON.parse(fileData);
    } catch (error) {
      console.error(`Error loading file ${filePath}:`, error);
      throw error;
    }
  });

  const [
    assessmentData,
    questionsData,
    // staticElementData,
    // sectionData,
    stepsData,
  ] = data;

  return {
    assessmentData,
    questionsData,
    // staticElementData,
    // sectionData,
    stepsData,
  };
};

// Generic function to query the database
const queryAsync = (connection, query, values) => {
  return new Promise((resolve, reject) => {
    connection.query(query, values, (error, results) => {
      if (error) {
        reject(error);
      } else {
        resolve(results);
      }
    });
  });
};

// Insert data into any table and return the inserted ids and codes
const insertData = async (client, table, columns, data, is_linking_table) => {
  if (data.length === 0) {
    console.log(`No data to insert for table ${table}.`);
    return [];
  }
  const placeholders = data.map((_, i) => `(${columns.map((_, j) => `$${i * columns.length + j + 1}`).join(', ')})`).join(', ');
  const returningColumns = !is_linking_table
    ? columns.includes('code') ? 'id, code' : 'id'
    : null;

  const query = `INSERT INTO ${table} (${columns.join(', ')}) VALUES ${placeholders}` +
    (returningColumns ? ` RETURNING ${returningColumns}` : '') + ';';
  const values = data.flatMap(d => columns.map(col => d[col] ?? null));
  const res = await client.query(query, values);
  console.log(`Inserted ${res.rowCount} row(s) into ${table}.`);
  const result = res.rows.reduce((acc, row) => {
    if (row.code) {
      acc[row.code] = row.id; // Add to object only if code exists
    }
    return acc;
  }, {});
  return result;
};

// Insert steps and step layouts
const insertStepsAndLayouts = async (client, stepsData, assessmentId, questionData) => {
  console.log('----------Inserting data into assessment_steps table----------');
  
  const stepDataList = [];
  const layoutDataList = [];

  for (const step of stepsData) {
    const stepData = {
      assessment_id: assessmentId,
      code: step.code,
      display_order: step.display_order,
      navigation_type: step.navigation_type,
      display_type: step.display_type || 'INDEPENDENT',
      visibility_configuration: step?.visibility_configuration || {},
      display_configuration : {
        code: step?.display_configuration?.code || null,
        title: step?.display_configuration?.title || null,
        sub_title: step?.display_configuration?.sub_title || null,
        description: step?.display_configuration?.description || null,
      },
      status: 'ACTIVE',
      created_on: new Date(),
      created_by: 1,
      updated_on: new Date(),
      updated_by: 1
    };
    stepDataList.push(stepData);

    // Prepare layouts for this section
    if (step.layout) {
      step?.layout.forEach(layout => {
        let childId;
        if (layout.type === "QUESTION") {
          childId = questionData[layout.code]
        } else if (layout.type === "STATIC_ELEMENT") {
          childId = staticElementsData[layout.code]
        } else if (layout.type === "SECTION") {
          childId = sectionData[layout.code]
        }

        if (!childId) {
          console.log(`Skipping layout for code: ${layout.code} (no matching child found)`);
          return;
        }

        layoutDataList.push({
          code: step.code,
          child_id: childId,
          child_type: layout.type,
          layout_configuration: layout?.layout_configuration || {},
          created_on: new Date(),
          created_by: 1,
          updated_on: new Date(),
          updated_by: 1
        });
      });
    }

  }

  // Insert sections and get IDs
  const insertedSteps = await insertData(client, 'assessment_steps', Object.keys(stepDataList[0]), stepDataList, false);
  console.log('----------Inserting assessment_step_layouts steps----------');

  // Finalize layoutDataList by attaching section IDs
  layoutDataList.forEach(layout => {
    layout.assessment_step_id = insertedSteps[layout.code];
    delete layout.code;
  });

  // Insert all layouts at once
  if (layoutDataList.length > 0) {
    await insertData(client, 'assessment_step_layouts', Object.keys(layoutDataList[0]), layoutDataList, false);
  }

  return insertedSteps;
};


// Insert sections and section layouts
const insertAssessmentStepSections = async (client, sectionsData, assessmentId, questionsData, staticElementsData) => {
  console.log('----------Inserting assessment_step_sections table---------');
  const sectionDataList = [];
  const layoutDataList = [];

  for (const section of sectionsData) {
    const sectionData = {
      assessment_id : assessmentId,
      code: section.code,
      visibility_configuration: section?.visibility_configuration || {},
      display_configuration : {
        code: section?.display_configuration?.code || null,
        title: section?.display_configuration?.title || null,
        description: section?.display_configuration?.description || null,
      },
      status: 'ACTIVE',
      created_on: new Date(),
      created_by: 1,
      updated_on: new Date(),
      updated_by: 1
    };
    sectionDataList.push(sectionData);

    // Prepare layouts for this section
    section.layout.forEach(layout => {
      let childId;
      if (layout.type === "QUESTION") {
        childId = questionsData[layout.code]
      } else if (layout.type === "STATIC_ELEMENT") {
        childId = staticElementsData[layout.code]
      }
      if (!childId) {
        console.log(`Skipping layout for code: ${layout.code} (no matching child found)`);
        return;
      }

      layoutDataList.push({
        code: section.code,
        child_id: childId,
        child_type: layout.type,
        layout_configuration: layout.layout_configuration || {},
        created_on: new Date(),
        created_by: 1,
        updated_on: new Date(),
        updated_by: 1
      });
    });
  }

  // Insert sections and get IDs
  const insertedSections = await insertData(client, 'assessment_step_sections', Object.keys(sectionDataList[0]), sectionDataList, false);
  console.log('----------Inserting assessment_step_section_layouts table---------');
  // Finalize layoutDataList by attaching section IDs
  layoutDataList.forEach(layout => {
    layout.assessment_step_section_id = insertedSections[layout.code];
    delete layout.code;
  });

  // Insert all layouts at once
  if (layoutDataList.length > 0) {
    await insertData(client, 'assessment_step_section_layouts', Object.keys(layoutDataList[0]), layoutDataList, false);
  }

  return insertedSections;
};

// Insert static elements
// const addStaticElement = async (client, staticElementData, assessmentId) => {
//   console.log('----------Inserting data into assessment_static_elements table----------');
//   const staticElementColumns = ['code', 'assessment_id', 'element_type', 'display_configuration', 'visibility_configuration', 'status', 'created_on', 'created_by', 'updated_on', 'updated_by'];
//   const staticElements = staticElementData.map(element => ({
//     code: element.code,
//     assessment_id: assessmentId,
//     element_type: element.element_type,
//     display_configuration: JSON.stringify(element.display_configuration || null),
//     visibility_configuration: JSON.stringify(element.visibility_configuration || null),
//     status: element.status || 'ACTIVE',
//     created_on: new Date(),
//     created_by: 1,
//     updated_on: new Date(),
//     updated_by: 1
//   }));
//   const result = await insertData(client, 'assessment_static_elements', staticElementColumns, staticElements, false);
//   return result;
// };

// Insert questions in assessment_questions table
const insertQuestions = async (client, questionsData, assessmentId) => {
  console.log('-----------Inserting data into  assessment_questions table----------');
  const questionColumns = ['code', 'assessment_id', 'visibility_configuration', 'display_configuration', 'status', 'created_on', 'created_by', 'updated_on', 'updated_by'];
  const questions = questionsData.map(question => ({
    code: question.code,
    assessment_id: assessmentId,
    visibility_configuration: question.visibility_configuration || {},
    display_configuration: question.display_configuration || {},
    status: question.status || 'ACTIVE',
    created_on: question.created_on || new Date(),
    created_by: question.created_by || 1,
    updated_on: question.updated_on || new Date(),
    updated_by: question.updated_by || 1
  }));
  const result = await insertData(client, 'assessment_questions', questionColumns, questions, false);
  return result;
};

// Insert assessment in assessment table
const insertAssessment = async (client, assessmentData) => {
  console.log('-----------Inserting data into assessments table----------');
  const assessmentColumns = ['code', 'name', 'title', 'description', 'status', 'created_on', 'created_by', 'updated_on', 'updated_by'];
  const assessments = {
    code: assessmentData.code,
    name: assessmentData.name,
    title: assessmentData.title || null,
    description: assessmentData.description || null,
    status: assessmentData.status || 'ACTIVE',
    created_on: assessmentData.created_on || new Date(),
    created_by: assessmentData.created_by || 1,
    updated_on: assessmentData.updated_on || new Date(),
    updated_by: assessmentData.updated_by || 1
  }
  const result = await insertData(client, 'assessments', assessmentColumns, [assessments], false);
  return result[assessmentData.code];
};

// Main process to handle all data insertion
const processData = async (client) => {
  try {
    console.log('Starting database transaction...');
    await client.query('BEGIN');

    let data = loadAllData()

    const assessmentId = await insertAssessment(client, data.assessmentData);

    const questionIdCode = await insertQuestions(client, data.questionsData, assessmentId);

    // const insertedStaticElements = await addStaticElement(client, data.staticElementData, assessmentId);
    // Insert sections and section layouts
    // const insertedSections = await insertAssessmentStepSections(client, data.sectionData, assessmentId, questionIdCode, insertedStaticElements);

    // Insert steps and step layouts
    await insertStepsAndLayouts(client, data.stepsData, assessmentId, questionIdCode);
    await client.query('COMMIT');
    console.log('Transaction committed. Data insertion completed successfully!');
  } catch (err) {
    await client.query('ROLLBACK');
    console.error('Error occurred. Rolling back transaction.', err);
  } finally {
    await client.end();
    console.log('Database connection closed.');
  }
};

// Main entry point
const main = async () => {
  console.log('Connecting to the database...');
  const client = new Client(cred);
  await client.connect();
  console.log('Database connected.');
  await processData(client);
};

main().catch(console.error);
